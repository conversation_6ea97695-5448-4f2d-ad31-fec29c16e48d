<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Model Viewer</title>
    <style>
        body { margin: 0; padding: 0; overflow: hidden; }
        .model-container { width: 100%; height: 100vh; }
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.8);
            padding: 15px 20px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            text-align: center;
        }
        .error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #f8d7da;
            color: #721c24;
            padding: 15px 20px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 80%;
        }
    </style>
</head>
<body>
    <div class="model-container">
        <div id="loading" class="loading">
            <div><PERSON><PERSON> tải mô hình 3D...</div>
        </div>
        <canvas id="canvas"></canvas>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/controls/OrbitControls.js"></script>

    <script>
        // Get model URL from query parameter
        const urlParams = new URLSearchParams(window.location.search);
        let modelUrl = urlParams.get('model');

        if (!modelUrl) {
            showError('Không tìm thấy đường dẫn đến mô hình 3D');
        } else {
            // Make sure the URL is absolute
            if (!modelUrl.startsWith('http') && !modelUrl.startsWith('//')) {
                // If it's a relative URL, make it absolute
                const baseUrl = window.location.origin;
                if (modelUrl.startsWith('/')) {
                    modelUrl = baseUrl + modelUrl;
                } else {
                    modelUrl = baseUrl + '/' + modelUrl;
                }
            }

            console.log('Loading model from URL:', modelUrl);

            // Initialize the viewer directly
            initViewer(modelUrl);
        }

        function initViewer(modelUrl) {
            const canvas = document.getElementById('canvas');
            const loading = document.getElementById('loading');

            // Create scene
            const scene = new THREE.Scene();
            scene.background = new THREE.Color(0xf8f8f8);

            // Create camera
            const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.z = 5;

            // Create renderer
            const renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(window.devicePixelRatio);

            // Add lights
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.7);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(1, 1, 1);
            scene.add(directionalLight);

            // Add controls
            const controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.autoRotate = true;
            controls.autoRotateSpeed = 1.0;

            // Handle window resize
            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });

            // Load model
            const loader = new THREE.GLTFLoader();
            loader.load(
                modelUrl,
                (gltf) => {
                    // Hide loading indicator
                    loading.style.display = 'none';

                    // Center model
                    const box = new THREE.Box3().setFromObject(gltf.scene);
                    const center = new THREE.Vector3();
                    box.getCenter(center);
                    const size = new THREE.Vector3();
                    box.getSize(size);

                    // Reset model position to center
                    gltf.scene.position.x = -center.x;
                    gltf.scene.position.y = -center.y;
                    gltf.scene.position.z = -center.z;

                    // Adjust camera position based on model size
                    const maxDim = Math.max(size.x, size.y, size.z);
                    const fov = camera.fov * (Math.PI / 180);
                    let cameraDistance = maxDim / (2 * Math.tan(fov / 2));
                    cameraDistance *= 1.5;
                    camera.position.z = cameraDistance;

                    // Add model to scene
                    scene.add(gltf.scene);
                },
                (xhr) => {
                    // Show loading progress
                    if (xhr.lengthComputable) {
                        const percent = Math.floor((xhr.loaded / xhr.total) * 100);
                        loading.innerHTML = `<div>Đang tải mô hình 3D: ${percent}%</div>`;
                    }
                },
                (error) => {
                    // Show error
                    showError(`Lỗi khi tải mô hình: ${error.message || 'Không xác định'}`);
                    console.error('Error loading model:', error);
                }
            );

            // Animation loop
            function animate() {
                requestAnimationFrame(animate);
                controls.update();
                renderer.render(scene, camera);
            }

            animate();
        }

        function showError(message) {
            const loading = document.getElementById('loading');
            if (loading) {
                loading.style.display = 'none';
            }

            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.innerHTML = `
                <h4>Không thể tải mô hình 3D</h4>
                <p>${message}</p>
                <p>Có thể do một trong các nguyên nhân sau:</p>
                <ul>
                    <li>Định dạng file không được hỗ trợ</li>
                    <li>Đường dẫn đến file không chính xác</li>
                    <li>File mô hình bị hỏng</li>
                </ul>
            `;
            document.body.appendChild(errorDiv);
        }
    </script>
</body>
</html>
