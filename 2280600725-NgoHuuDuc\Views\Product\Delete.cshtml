﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@model NgoHuuDuc_2280600725.Models.Product

@{
    ViewData["Title"] = "<PERSON><PERSON><PERSON> sản phẩm";
}

<h2><PERSON><PERSON><PERSON> sản phẩm</h2>

<div class="alert alert-danger">
    <h3>Bạn có chắc chắn muốn xóa sản phẩm này?</h3>
</div>

<div class="row">
    <div class="col-md-6">
        <dl class="row">
            <dt class="col-sm-4">Tên sản phẩm</dt>
            <dd class="col-sm-8">@Model.Name</dd>

            <dt class="col-sm-4">Giá</dt>
            <dd class="col-sm-8">@Model.Price.ToString("C")</dd>

            <dt class="col-sm-4"><PERSON><PERSON> tả</dt>
            <dd class="col-sm-8">
                @{
                    var description = Model.Description;
                    var sizeTag = "[SIZES]";
                    var endSizeTag = "[/SIZES]";
                    var reviewTag = "[REVIEWS]";
                    var endReviewTag = "[/REVIEWS]";

                    // Loại bỏ phần kỹ thuật khỏi mô tả
                    if (!string.IsNullOrEmpty(description))
                    {
                        // Loại bỏ phần kích thước
                        if (description.Contains(sizeTag) && description.Contains(endSizeTag))
                        {
                            var startIndex = description.IndexOf(sizeTag);
                            var endIndex = description.IndexOf(endSizeTag) + endSizeTag.Length;
                            if (startIndex < endIndex)
                            {
                                description = description.Remove(startIndex, endIndex - startIndex);
                            }
                        }

                        // Loại bỏ phần đánh giá
                        if (description.Contains(reviewTag) && description.Contains(endReviewTag))
                        {
                            var startIndex = description.IndexOf(reviewTag);
                            var endIndex = description.IndexOf(endReviewTag) + endReviewTag.Length;
                            if (startIndex < endIndex)
                            {
                                description = description.Remove(startIndex, endIndex - startIndex);
                            }
                        }

                        // Loại bỏ khoảng trắng thừa
                        description = description.Trim();
                    }
                }
                @description
            </dd>

            <dt class="col-sm-4">Danh mục</dt>
            <dd class="col-sm-8">@Model.Category?.Name</dd>

            @if (!string.IsNullOrEmpty(Model.ImageUrl))
            {
                <dt class="col-sm-4">Hình ảnh</dt>
                <dd class="col-sm-8">
                    <img src="@Model.ImageUrl" alt="@Model.Name" class="img-thumbnail" style="max-width: 200px;" />
                </dd>
            }
        </dl>

        <form asp-action="Delete" method="post">
            <input type="hidden" asp-for="Id" />
            <button type="submit" class="btn btn-danger">Xóa</button>
            <a asp-action="Index" class="btn btn-secondary">Hủy</a>
        </form>
    </div>
</div>
