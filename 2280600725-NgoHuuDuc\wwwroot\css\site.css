html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

/* Sticky footer styles */

body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* 3D Model Viewer Styles */
.product-view-container {
  position: relative;
}

.product-view {
  transition: all 0.3s ease;
}

.product-view.active {
  display: block;
}

#model-container {
  border-radius: 0.25rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.view-toggle-buttons {
  margin-top: 10px;
}

/* Add a small instruction overlay for 3D viewer */
#product-3d-view::after {
  content: "Kéo để xoay • Cuộn để phóng to/thu nhỏ";
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
}