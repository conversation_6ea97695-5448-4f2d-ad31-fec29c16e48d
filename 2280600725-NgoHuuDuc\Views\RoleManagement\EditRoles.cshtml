@using NgoHuuDuc_2280600725.Models.AccountViewModels
@model UserRolesViewModel

<div class="container mt-4">
    <h2>Edit Roles for @Model.UserName</h2>
    
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <form asp-action="EditRoles" method="post">
        <input type="hidden" asp-for="UserId" />
        
        <div class="form-group">
            <label>Email: @Model.Email</label>
        </div>
        
        <div class="form-group">
            <label>Select Roles:</label>
            @foreach (var role in Model.AvailableRoles)
            {
                <div class="form-check">
                    <input type="checkbox" name="roles" value="@role" class="form-check-input"
                           @(Model.CurrentRoles.Contains(role) ? "checked" : "") />
                    <label class="form-check-label">@role</label>
                </div>
            }
        </div>

        <div class="mt-3">
            <button type="submit" class="btn btn-primary">Save Changes</button>
            <a asp-action="Index" class="btn btn-secondary">Back to List</a>
        </div>
    </form>
</div>
