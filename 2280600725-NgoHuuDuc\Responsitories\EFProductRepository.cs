﻿using Microsoft.EntityFrameworkCore;
using NgoHuuDuc_2280600725.Data;
using NgoHuuDuc_2280600725.Helpers;
using NgoHuuDuc_2280600725.Models;

namespace NgoHuuDuc_2280600725.Responsitories
{
    public class EFProductRepository : IProductRepository
    {
        private readonly ApplicationDbContext _context;

        public EFProductRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Product>> GetProductsByCategoryAsync(int? categoryId)
        {
            var query = _context.Products.Include(p => p.Category).AsQueryable();
            if (categoryId.HasValue)
            {
                query = query.Where(p => p.CategoryId == categoryId.Value);
            }
            return await query.ToListAsync();
        }

        public async Task<Product> GetProductByIdAsync(int id)
        {
            // L<PERSON>y sản phẩm theo Id, bao gồm cả thông tin Category liên quan
            return await _context.Products.Include(p => p.Category).FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task AddProductAsync(Product product)
        {
            _context.Products.Add(product);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateProductAsync(Product product)
        {
            try
            {
                // Lấy sản phẩm hiện tại từ cơ sở dữ liệu
                var existingProduct = await _context.Products.FindAsync(product.Id);
                if (existingProduct == null)
                {
                    throw new Exception($"Product with ID {product.Id} not found");
                }

                // Cập nhật các thuộc tính cơ bản
                existingProduct.Name = product.Name;
                existingProduct.Description = product.Description;
                existingProduct.Price = product.Price;
                existingProduct.Quantity = product.Quantity;

                existingProduct.CategoryId = product.CategoryId;
                existingProduct.ImageUrl = product.ImageUrl;
                existingProduct.Model3DUrl = product.Model3DUrl;

                // Lưu thay đổi vào database
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log lỗi và ném lại ngoại lệ
                Console.WriteLine($"Error updating product: {ex.Message}");
                throw;
            }
        }

        public async Task DeleteProductAsync(int id)
        {
            var product = await _context.Products.FindAsync(id);
            if (product != null)
            {
                _context.Products.Remove(product);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ProductExistsAsync(int id) // Fix: Implement ProductExistsAsync
        {
            // Kiểm tra xem sản phẩm có tồn tại trong database không
            return await _context.Products.AnyAsync(p => p.Id == id);
        }

        public async Task<Product> GetProductWithCategoryByIdAsync(int id) // Fix: Implement GetProductWithCategoryByIdAsync
        {
            // Lấy sản phẩm theo Id, bao gồm cả thông tin Category liên quan
            return await _context.Products.Include(p => p.Category).FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<IEnumerable<Product>> SearchProductsAsync(string keyword)
        {
            if (string.IsNullOrWhiteSpace(keyword))
                return await GetProductsByCategoryAsync(null);

            keyword = keyword.ToLower();
            // Tìm kiếm sản phẩm theo tên, mô tả hoặc tên danh mục (không phân biệt hoa thường)
            return await _context.Products
                .Include(p => p.Category)
                .Where(p => p.Name.ToLower().Contains(keyword) ||
                           p.Description.ToLower().Contains(keyword) ||
                           p.Category.Name.ToLower().Contains(keyword))
                .ToListAsync();
        }

        public async Task<PaginatedList<Product>> GetProductsByCategoryAsync(int? categoryId, int pageIndex, int pageSize)
        {
            var query = _context.Products.Include(p => p.Category).AsQueryable();
            if (categoryId.HasValue)
            {
                query = query.Where(p => p.CategoryId == categoryId.Value);
            }
            // Trả về danh sách phân trang sản phẩm theo danh mục
            return await PaginatedList<Product>.CreateAsync(query, pageIndex, pageSize);
        }

        public async Task<PaginatedList<Product>> GetProductsByCategoryAsync(int? categoryId, int pageIndex, int pageSize, bool includeHidden)
        {
            var query = _context.Products.Include(p => p.Category).AsQueryable();

            if (categoryId.HasValue)
            {
                query = query.Where(p => p.CategoryId == categoryId.Value);
            }

            if (!includeHidden)
            {
                // Lọc ra các sản phẩm không bị ẩn
                query = query.Where(p => !p.IsHidden);
            }

            return await PaginatedList<Product>.CreateAsync(query, pageIndex, pageSize);
        }

        public async Task<PaginatedList<Product>> SearchProductsAsync(string keyword, int pageIndex, int pageSize)
        {
            if (string.IsNullOrWhiteSpace(keyword))
                return await GetProductsByCategoryAsync(null, pageIndex, pageSize);

            keyword = keyword.ToLower();
            var query = _context.Products
                .Include(p => p.Category)
                .Where(p => p.Name.ToLower().Contains(keyword) ||
                           p.Description.ToLower().Contains(keyword) ||
                           p.Category.Name.ToLower().Contains(keyword));

            return await PaginatedList<Product>.CreateAsync(query, pageIndex, pageSize);
        }

        public async Task<PaginatedList<Product>> SearchProductsAsync(string keyword, int pageIndex, int pageSize, bool includeHidden)
        {
            if (string.IsNullOrWhiteSpace(keyword))
                return await GetProductsByCategoryAsync(null, pageIndex, pageSize, includeHidden);

            keyword = keyword.ToLower();
            var query = _context.Products
                .Include(p => p.Category)
                .Where(p => p.Name.ToLower().Contains(keyword) ||
                            p.Description.ToLower().Contains(keyword) ||
                            p.Category.Name.ToLower().Contains(keyword));

            if (!includeHidden)
            {
                // Lọc ra các sản phẩm không bị ẩn
                query = query.Where(p => !p.IsHidden);
            }

            return await PaginatedList<Product>.CreateAsync(query, pageIndex, pageSize);
        }

        public async Task<PaginatedList<Product>> GetProductsByCategoryAsync(int? categoryId, int pageIndex, int pageSize, bool? includeHidden, string sortBy = "", string order = "asc")
        {
            var query = _context.Products.Include(p => p.Category).AsQueryable();

            // Lọc theo danh mục nếu có
            if (categoryId.HasValue)
            {
                query = query.Where(p => p.CategoryId == categoryId.Value);
            }

            // Lọc sản phẩm bị ẩn nếu cần
            if (includeHidden.HasValue && !includeHidden.Value)
            {
                query = query.Where(p => !p.IsHidden);
            }

            // Sắp xếp theo trường được chọn (price, name, stock)
            if (!string.IsNullOrEmpty(sortBy))
            {
                switch (sortBy.ToLower())
                {
                    case "price":
                        query = order.ToLower() == "desc"
                            ? query.OrderByDescending(p => p.Price)
                            : query.OrderBy(p => p.Price);
                        break;
                    case "name":
                        query = order.ToLower() == "desc"
                            ? query.OrderByDescending(p => p.Name)
                            : query.OrderBy(p => p.Name);
                        break;
                    case "stock":
                        query = order.ToLower() == "desc"
                            ? query.OrderByDescending(p => p.Quantity)
                            : query.OrderBy(p => p.Quantity);
                        break;
                    default:
                        // Mặc định sắp xếp theo tên tăng dần
                        query = query.OrderBy(p => p.Name);
                        break;
                }
            }
            else
            {
                // Mặc định sắp xếp theo tên tăng dần
                query = query.OrderBy(p => p.Name);
            }

            return await PaginatedList<Product>.CreateAsync(query, pageIndex, pageSize);
        }

        public async Task<PaginatedList<Product>> SearchProductsAsync(string keyword, int pageIndex, int pageSize, bool includeHidden, string sortBy = "", string order = "asc")
        {
            if (string.IsNullOrWhiteSpace(keyword))
                return await GetProductsByCategoryAsync(null, pageIndex, pageSize, includeHidden, sortBy, order);

            keyword = keyword.ToLower();
            var query = _context.Products
                .Include(p => p.Category)
                .Where(p => p.Name.ToLower().Contains(keyword) ||
                            p.Description.ToLower().Contains(keyword) ||
                            p.Category.Name.ToLower().Contains(keyword));

            if (!includeHidden)
            {
                // Lọc ra các sản phẩm không bị ẩn
                query = query.Where(p => !p.IsHidden);
            }

            // Sắp xếp theo trường được chọn (price, name, stock)
            if (!string.IsNullOrEmpty(sortBy))
            {
                switch (sortBy.ToLower())
                {
                    case "price":
                        query = order.ToLower() == "desc"
                            ? query.OrderByDescending(p => p.Price)
                            : query.OrderBy(p => p.Price);
                        break;
                    case "name":
                        query = order.ToLower() == "desc"
                            ? query.OrderByDescending(p => p.Name)
                            : query.OrderBy(p => p.Name);
                        break;
                    case "stock":
                        query = order.ToLower() == "desc"
                            ? query.OrderByDescending(p => p.Quantity)
                            : query.OrderBy(p => p.Quantity);
                        break;
                    default:
                        // Mặc định sắp xếp theo tên tăng dần
                        query = query.OrderBy(p => p.Name);
                        break;
                }
            }
            else
            {
                // Mặc định sắp xếp theo tên tăng dần
                query = query.OrderBy(p => p.Name);
            }

            return await PaginatedList<Product>.CreateAsync(query, pageIndex, pageSize);
        }

        public async Task<PaginatedList<Product>> GetProductsByCategoryAsync(int? categoryId, int pageIndex, int pageSize, bool? includeHidden, string sortBy = "", string order = "asc", bool? inStock = null)
        {
            var query = _context.Products.Include(p => p.Category).AsQueryable();

            // Lọc theo danh mục nếu có
            if (categoryId.HasValue)
            {
                query = query.Where(p => p.CategoryId == categoryId.Value);
            }

            // Lọc sản phẩm bị ẩn nếu cần
            if (includeHidden.HasValue && !includeHidden.Value)
            {
                query = query.Where(p => !p.IsHidden);
            }

            // Lọc sản phẩm còn hàng hoặc hết hàng nếu có yêu cầu
            if (inStock.HasValue)
            {
                query = inStock.Value
                    ? query.Where(p => p.Quantity > 0)
                    : query.Where(p => p.Quantity == 0);
            }

            // Sắp xếp theo trường được chọn (price, name, stock)
            if (!string.IsNullOrEmpty(sortBy))
            {
                switch (sortBy.ToLower())
                {
                    case "price":
                        query = order.ToLower() == "desc"
                            ? query.OrderByDescending(p => p.Price)
                            : query.OrderBy(p => p.Price);
                        break;
                    case "name":
                        query = order.ToLower() == "desc"
                            ? query.OrderByDescending(p => p.Name)
                            : query.OrderBy(p => p.Name);
                        break;
                    case "stock":
                        query = order.ToLower() == "desc"
                            ? query.OrderByDescending(p => p.Quantity)
                            : query.OrderBy(p => p.Quantity);
                        break;
                }
            }

            return await PaginatedList<Product>.CreateAsync(query, pageIndex, pageSize);
        }

    }
}
