@model IEnumerable<NgoHuuDuc_2280600725.Models.Order>
@using NgoHuuDuc_2280600725.Models.Enums
@using NgoHuuDuc_2280600725.Extensions

@{
    ViewData["Title"] = "Quản lý đơn hàng";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-shopping-cart me-2" style="color: var(--elegant-gold);"></i>
            Quản lý đơn hàng
        </h1>
    </div>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="card card-elegant">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Mã đơn hàng</th>
                            <th>Ngày đặt</th>
                            <th>Khách hàng</th>
                            <th>Tổng tiền</th>
                            <th>Địa chỉ giao hàng</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var order in Model)
                        {
                            <tr>
                                <td>@order.Id</td>
                                <td>@order.OrderDate.ToString("dd/MM/yyyy HH:mm")</td>
                                <td>@(order.User?.FullName ?? "Khách hàng không xác định")</td>
                                <td>@order.TotalPrice.ToString("C0")</td>
                                <td>@order.ShippingAddress</td>
                                <td>
                                    @{
                                        var badgeClass = order.Status switch
                                        {
                                            OrderStatus.Pending => "bg-warning",
                                            OrderStatus.Confirmed => "bg-info",
                                            OrderStatus.Shipping => "bg-primary",
                                            OrderStatus.Delivered => "bg-success",
                                            OrderStatus.Cancelled => "bg-danger",
                                            OrderStatus.Returned => "bg-secondary",
                                            _ => "bg-light"
                                        };
                                    }
                                    <span class="badge @badgeClass">@order.Status.GetDisplayName()</span>
                                </td>
                                <td>
                                    <a asp-action="Details" asp-route-id="@order.Id" class="btn btn-elegant-secondary btn-sm">
                                        <i class="fas fa-eye me-1"></i> Chi tiết
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('table').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Vietnamese.json"
                },
                "order": [[1, "desc"]]
            });
        });
    </script>
}
