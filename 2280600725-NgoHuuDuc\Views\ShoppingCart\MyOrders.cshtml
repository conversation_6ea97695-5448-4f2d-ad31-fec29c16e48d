@model List<NgoHuuDuc_2280600725.Models.Order>
@using NgoHuuDuc_2280600725.Models.Enums
@using NgoHuuDuc_2280600725.Extensions

@{
    ViewData["Title"] = "Đơn hàng của tôi";
}

<div class="container mt-4">
    <h1 class="mb-4">
        <i class="fas fa-shopping-bag me-2" style="color: var(--elegant-gold);"></i>
        Đơn hàng của tôi
    </h1>

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (ViewBag.Message != null)
    {
        <div class="alert alert-info">
            @ViewBag.Message
            <p class="mt-2">
                <a asp-controller="Home" asp-action="Index" class="btn btn-elegant-primary">
                    <i class="fas fa-shopping-cart me-1"></i> Bắt đầu mua sắm
                </a>
            </p>
        </div>
    }
    else if (Model.Any())
    {
        <div class="card card-elegant">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Mã đơn hàng</th>
                                <th>Ngày đặt</th>
                                <th>Tổng tiền</th>
                                <th>Địa chỉ giao hàng</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var order in Model)
                            {
                                <tr>                                    <td>@order.Id</td>
                                    <td>@order.OrderDate.ToString("dd/MM/yyyy HH:mm")</td>
                                    <td>@order.TotalPrice.ToString("N0")đ</td>
                                    <td>@order.ShippingAddress</td>
                                    <td>
                                        @{
                                            var badgeClass = order.Status switch
                                            {
                                                OrderStatus.Pending => "bg-warning",
                                                OrderStatus.Confirmed => "bg-info",
                                                OrderStatus.Shipping => "bg-primary",
                                                OrderStatus.Delivered => "bg-success",
                                                OrderStatus.Cancelled => "bg-danger",
                                                OrderStatus.Returned => "bg-secondary",
                                                _ => "bg-light"
                                            };
                                        }
                                        <span class="badge @badgeClass">@order.Status.GetDisplayName()</span>
                                    </td>
                                    <td>
                                        <a asp-action="OrderDetails" asp-route-id="@order.Id" class="btn btn-elegant-secondary btn-sm">
                                            <i class="fas fa-eye me-1"></i> Xem chi tiết
                                        </a>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
</div>
