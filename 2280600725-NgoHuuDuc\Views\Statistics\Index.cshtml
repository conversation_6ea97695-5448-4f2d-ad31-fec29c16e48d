@model NgoHuuDuc_2280600725.DTOs.StatisticsOverviewDTO
@using NgoHuuDuc_2280600725.Models.Enums
@using NgoHuuDuc_2280600725.Extensions

@{
    ViewData["Title"] = "Thống kê";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-line me-2" style="color: var(--elegant-gold);"></i>
            Thống kê
        </h1>
    </div>

    <!-- B<PERSON> lọc thời gian -->
    <div class="card card-elegant mb-4">
        <div class="card-body">
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="startDate" class="form-label">Từ ngày</label>
                    <input type="date" id="startDate" name="startDate" class="form-control" value="@ViewBag.StartDate" />
                </div>
                <div class="col-md-4">
                    <label for="endDate" class="form-label">Đến ngày</label>
                    <input type="date" id="endDate" name="endDate" class="form-control" value="@ViewBag.EndDate" />
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i> Lọc
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Thống kê tổng quan -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Tổng đơn hàng
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalOrders</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Doanh thu (Đã giao hàng)
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalRevenue.ToString("C0")</div>
                            <div class="text-xs text-muted">Chỉ tính từ đơn hàng đã giao</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Tổng sản phẩm
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalProducts</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Tổng người dùng
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalUsers</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Thống kê theo trạng thái đơn hàng -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card card-elegant mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold">Thống kê đơn hàng theo trạng thái</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4">
                        <canvas id="orderStatusChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        <span class="mr-2">
                            <i class="fas fa-circle text-warning"></i> Chờ xác nhận (@Model.OrdersByStatus.Pending)
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-info"></i> Đã xác nhận (@Model.OrdersByStatus.Confirmed)
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-primary"></i> Đang giao hàng (@Model.OrdersByStatus.Shipping)
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-success"></i> Đã giao hàng (@Model.OrdersByStatus.Delivered)
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-danger"></i> Đã hủy (@Model.OrdersByStatus.Cancelled)
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-secondary"></i> Hoàn trả (@Model.OrdersByStatus.Returned)
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card card-elegant mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold">Thống kê doanh thu theo trạng thái</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4">
                        <canvas id="revenueStatusChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        <span class="mr-2">
                            <i class="fas fa-circle text-warning"></i> Chờ xác nhận (@Model.RevenueByStatus.Pending.ToString("C0"))
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-info"></i> Đã xác nhận (@Model.RevenueByStatus.Confirmed.ToString("C0"))
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-primary"></i> Đang giao hàng (@Model.RevenueByStatus.Shipping.ToString("C0"))
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-success"></i> Đã giao hàng (@Model.RevenueByStatus.Delivered.ToString("C0"))
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-danger"></i> Đã hủy (@Model.RevenueByStatus.Cancelled.ToString("C0"))
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-secondary"></i> Hoàn trả (@Model.RevenueByStatus.Returned.ToString("C0"))
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Thông báo về API -->
    <div class="card card-elegant mb-4">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-info-circle me-2"></i>
                Thông tin API
            </h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h6><i class="fas fa-code me-2"></i>API Endpoints đã sẵn sàng!</h6>
                <p class="mb-2">Hệ thống đã được cập nhật để sử dụng DTOs và cung cấp các API endpoints:</p>
                <ul class="mb-2">
                    <li><code>GET /api/StatisticsApi/overview</code> - Thống kê tổng quan</li>
                    <li><code>GET /api/StatisticsApi/daily-revenue</code> - Doanh thu theo ngày</li>
                    <li><code>GET /api/StatisticsApi/top-products</code> - Top sản phẩm bán chạy</li>
                </ul>
                <a href="/swagger" target="_blank" class="btn btn-primary btn-sm">
                    <i class="fas fa-external-link-alt me-1"></i>
                    Xem API Documentation
                </a>
                <a href="/Home/ApiTest" target="_blank" class="btn btn-outline-info btn-sm ms-2">
                    <i class="fas fa-flask me-1"></i>
                    Test API
                </a>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Biểu đồ trạng thái đơn hàng
        var orderStatusCtx = document.getElementById('orderStatusChart').getContext('2d');
        var orderStatusChart = new Chart(orderStatusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Chờ xác nhận', 'Đã xác nhận', 'Đang giao hàng', 'Đã giao hàng', 'Đã hủy', 'Hoàn trả'],
                datasets: [{
                    data: [@Model.OrdersByStatus.Pending, @Model.OrdersByStatus.Confirmed, @Model.OrdersByStatus.Shipping, @Model.OrdersByStatus.Delivered, @Model.OrdersByStatus.Cancelled, @Model.OrdersByStatus.Returned],
                    backgroundColor: ['#f6c23e', '#36b9cc', '#4e73df', '#1cc88a', '#e74a3b', '#858796'],
                    hoverBackgroundColor: ['#f6c23e', '#36b9cc', '#4e73df', '#1cc88a', '#e74a3b', '#858796'],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }],
            },
            options: {
                maintainAspectRatio: false,
                tooltips: {
                    backgroundColor: "rgb(255,255,255)",
                    bodyFontColor: "#858796",
                    borderColor: '#dddfeb',
                    borderWidth: 1,
                    xPadding: 15,
                    yPadding: 15,
                    displayColors: false,
                    caretPadding: 10,
                },
                legend: {
                    display: false
                },
                cutoutPercentage: 80,
            },
        });

        // Biểu đồ doanh thu theo trạng thái
        var revenueStatusCtx = document.getElementById('revenueStatusChart').getContext('2d');
        var revenueStatusChart = new Chart(revenueStatusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Chờ xác nhận', 'Đã xác nhận', 'Đang giao hàng', 'Đã giao hàng', 'Đã hủy', 'Hoàn trả'],
                datasets: [{
                    data: [@Model.RevenueByStatus.Pending, @Model.RevenueByStatus.Confirmed, @Model.RevenueByStatus.Shipping, @Model.RevenueByStatus.Delivered, @Model.RevenueByStatus.Cancelled, @Model.RevenueByStatus.Returned],
                    backgroundColor: ['#f6c23e', '#36b9cc', '#4e73df', '#1cc88a', '#e74a3b', '#858796'],
                    hoverBackgroundColor: ['#f6c23e', '#36b9cc', '#4e73df', '#1cc88a', '#e74a3b', '#858796'],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }],
            },
            options: {
                maintainAspectRatio: false,
                tooltips: {
                    backgroundColor: "rgb(255,255,255)",
                    bodyFontColor: "#858796",
                    borderColor: '#dddfeb',
                    borderWidth: 1,
                    xPadding: 15,
                    yPadding: 15,
                    displayColors: false,
                    caretPadding: 10,
                },
                legend: {
                    display: false
                },
                cutoutPercentage: 80,
            },
        });

        // Console log để debug
        console.log('Statistics DTO loaded successfully');
        console.log('Total Orders:', @Model.TotalOrders);
        console.log('Total Revenue:', @Model.TotalRevenue);
        console.log('API endpoints available at /swagger');
    </script>
}
