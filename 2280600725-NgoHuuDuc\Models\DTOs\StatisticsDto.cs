using System.ComponentModel.DataAnnotations;

namespace NgoHuuDuc_2280600725.Models.ViewModels
{
    /// <summary>
    /// DTO cho thống kê tổng quan
    /// </summary>
    public class StatisticsOverviewDto
    {
        /// <summary>
        /// Khoảng thời gian thống kê
        /// </summary>
        public DateRangeDto DateRange { get; set; } = new();

        /// <summary>
        /// Tổng số đơn hàng
        /// </summary>
        public int TotalOrders { get; set; }

        /// <summary>
        /// Tổng doanh thu (chỉ tính từ đơn hàng đã giao)
        /// </summary>
        public decimal TotalRevenue { get; set; }

        /// <summary>
        /// Tổng số sản phẩm
        /// </summary>
        public int TotalProducts { get; set; }

        /// <summary>
        /// Tổng số người dùng
        /// </summary>
        public int TotalUsers { get; set; }

        /// <summary>
        /// Thống kê đơn hàng theo trạng thái
        /// </summary>
        public OrdersByStatusDto OrdersByStatus { get; set; } = new();

        /// <summary>
        /// Thống kê doanh thu theo trạng thái
        /// </summary>
        public RevenueByStatusDto RevenueByStatus { get; set; } = new();
    }

    /// <summary>
    /// DTO cho khoảng thời gian
    /// </summary>
    public class DateRangeDto
    {
        /// <summary>
        /// Ngày bắt đầu
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// Ngày kết thúc
        /// </summary>
        public DateTime EndDate { get; set; }
    }

    /// <summary>
    /// DTO cho thống kê đơn hàng theo trạng thái
    /// </summary>
    public class OrdersByStatusDto
    {
        /// <summary>
        /// Số đơn hàng chờ xác nhận
        /// </summary>
        public int Pending { get; set; }

        /// <summary>
        /// Số đơn hàng đã xác nhận
        /// </summary>
        public int Confirmed { get; set; }

        /// <summary>
        /// Số đơn hàng đang giao
        /// </summary>
        public int Shipping { get; set; }

        /// <summary>
        /// Số đơn hàng đã giao
        /// </summary>
        public int Delivered { get; set; }

        /// <summary>
        /// Số đơn hàng đã hủy
        /// </summary>
        public int Cancelled { get; set; }

        /// <summary>
        /// Số đơn hàng hoàn trả
        /// </summary>
        public int Returned { get; set; }
    }

    /// <summary>
    /// DTO cho thống kê doanh thu theo trạng thái
    /// </summary>
    public class RevenueByStatusDto
    {
        /// <summary>
        /// Doanh thu từ đơn hàng chờ xác nhận
        /// </summary>
        public decimal Pending { get; set; }

        /// <summary>
        /// Doanh thu từ đơn hàng đã xác nhận
        /// </summary>
        public decimal Confirmed { get; set; }

        /// <summary>
        /// Doanh thu từ đơn hàng đang giao
        /// </summary>
        public decimal Shipping { get; set; }

        /// <summary>
        /// Doanh thu từ đơn hàng đã giao
        /// </summary>
        public decimal Delivered { get; set; }

        /// <summary>
        /// Doanh thu từ đơn hàng đã hủy
        /// </summary>
        public decimal Cancelled { get; set; }

        /// <summary>
        /// Doanh thu từ đơn hàng hoàn trả
        /// </summary>
        public decimal Returned { get; set; }
    }

    /// <summary>
    /// DTO cho thống kê doanh thu theo ngày
    /// </summary>
    public class DailyRevenueDto
    {
        /// <summary>
        /// Ngày
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Số đơn hàng trong ngày
        /// </summary>
        public int OrderCount { get; set; }

        /// <summary>
        /// Doanh thu trong ngày (chỉ tính từ đơn hàng đã giao)
        /// </summary>
        public decimal Revenue { get; set; }
    }

    /// <summary>
    /// DTO cho top sản phẩm bán chạy
    /// </summary>
    public class TopProductDto
    {
        /// <summary>
        /// ID sản phẩm
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// Tên sản phẩm
        /// </summary>
        public string ProductName { get; set; } = "";

        /// <summary>
        /// URL hình ảnh sản phẩm
        /// </summary>
        public string? ImageUrl { get; set; }

        /// <summary>
        /// Số lượng đã bán
        /// </summary>
        public int QuantitySold { get; set; }

        /// <summary>
        /// Doanh thu từ sản phẩm
        /// </summary>
        public decimal Revenue { get; set; }
    }
}
