﻿@model NgoHuuDuc_2280600725.Models.Category

@{
    ViewData["Title"] = "Xóa danh mục";
}

<h1>Xóa</h1>

<h3 class="text-danger">Bạn có chắc chắn muốn xóa danh mục này?</h3>
<div class="card">
    <div class="card-body">
        <h4 class="card-title"><PERSON><PERSON> mục</h4>
        <hr />
        <dl class="row">
            <dt class="col-sm-2">
                @Html.DisplayNameFor(model => model.Name)
            </dt>
            <dd class="col-sm-10">
                @Html.DisplayFor(model => model.Name)
            </dd>
            <dt class="col-sm-2">
                @Html.DisplayNameFor(model => model.Description)
            </dt>
            <dd class="col-sm-10">
                @Html.DisplayFor(model => model.Description)
            </dd>
        </dl>
    </div>
</div>

<form asp-action="Delete" class="mt-3">
    <input type="hidden" asp-for="Id" />
    <input type="submit" value="Xóa" class="btn btn-danger" 
           onclick="return confirm('Bạn có chắc chắn muốn xóa danh mục này?');" /> |
    <a asp-action="Index" class="btn btn-secondary">Quay lại danh sách</a>
</form>
