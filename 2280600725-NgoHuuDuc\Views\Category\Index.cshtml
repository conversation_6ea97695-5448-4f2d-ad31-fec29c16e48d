﻿@model IEnumerable<NgoHuuDuc_2280600725.Models.Category>
@{
    ViewData["Title"] = "Danh mục";
}

@if (TempData["Message"] != null)
{
    <div class="alert alert-success alert-dismissible fade show">
        @TempData["Message"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["Success"] != null)
{
    <div class="alert alert-success">
        @TempData["Success"]
    </div>
}

<div class="dashboard-card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <span><i class="fas fa-tags me-2"></i>Danh sách danh mục</span>
        <a asp-action="Create" class="btn btn-elegant-primary btn-sm">
            <i class="fas fa-plus-circle me-2"></i>Tạo mới
        </a>
    </div>
    <div class="card-body">
<div class="table-responsive">
    <table class="dashboard-table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.Name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Description)
            </th>
            <th>Thao tác</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.Name)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Description)
                </td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-elegant-primary">
                        <i class="fas fa-edit me-1"></i>Sửa
                    </a>
                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-elegant-secondary">
                        <i class="fas fa-info-circle me-1"></i>Chi tiết
                    </a>
                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash-alt me-1"></i>Xóa
                    </a>
                </td>
            </tr>
        }
    </tbody>
    </table>
</div>
    </div>
</div>
