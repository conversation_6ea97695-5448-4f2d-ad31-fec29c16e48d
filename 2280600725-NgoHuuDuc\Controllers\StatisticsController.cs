using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NgoHuuDuc_2280600725.Data;
using NgoHuuDuc_2280600725.Models;
using NgoHuuDuc_2280600725.Models.Enums;
using NgoHuuDuc_2280600725.DTOs;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace NgoHuuDuc_2280600725.Controllers
{
    [Authorize(Roles = "Administrator")]
    public class StatisticsController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public StatisticsController(ApplicationDbContext context, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        // GET: Statistics
        public async Task<IActionResult> Index(DateTime? startDate, DateTime? endDate)
        {
            // Mặc định là thống kê tháng hiện tại
            if (!startDate.HasValue)
            {
                startDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            }

            if (!endDate.HasValue)
            {
                endDate = DateTime.Now;
            }

            // Đảm bảo endDate là cuối ngày
            endDate = endDate.Value.Date.AddDays(1).AddTicks(-1);

            ViewBag.StartDate = startDate.Value.ToString("yyyy-MM-dd");
            ViewBag.EndDate = endDate.Value.ToString("yyyy-MM-dd");

            // Lấy tất cả đơn hàng trong khoảng thời gian
            var orders = await _context.Orders
                .Include(o => o.OrderDetails)
                .ThenInclude(od => od.Product)
                .Where(o => o.OrderDate >= startDate && o.OrderDate <= endDate)
                .ToListAsync();

            // Thống kê tổng quan sử dụng DTO
            var statistics = new StatisticsOverviewDTO
            {
                DateRange = new DateRangeDTO
                {
                    StartDate = startDate.Value,
                    EndDate = endDate.Value
                },
                TotalOrders = orders.Count,
                TotalRevenue = orders.Where(o => o.Status == OrderStatus.Delivered).Sum(o => o.TotalPrice),
                TotalProducts = await _context.Products.CountAsync(),
                TotalUsers = await _userManager.Users.CountAsync(),

                OrdersByStatus = new OrdersByStatusDTO
                {
                    Pending = orders.Count(o => o.Status == OrderStatus.Pending),
                    Confirmed = orders.Count(o => o.Status == OrderStatus.Confirmed),
                    Shipping = orders.Count(o => o.Status == OrderStatus.Shipping),
                    Delivered = orders.Count(o => o.Status == OrderStatus.Delivered),
                    Cancelled = orders.Count(o => o.Status == OrderStatus.Cancelled),
                    Returned = orders.Count(o => o.Status == OrderStatus.Returned)
                },

                RevenueByStatus = new RevenueByStatusDTO
                {
                    Pending = orders.Where(o => o.Status == OrderStatus.Pending).Sum(o => o.TotalPrice),
                    Confirmed = orders.Where(o => o.Status == OrderStatus.Confirmed).Sum(o => o.TotalPrice),
                    Shipping = orders.Where(o => o.Status == OrderStatus.Shipping).Sum(o => o.TotalPrice),
                    Delivered = orders.Where(o => o.Status == OrderStatus.Delivered).Sum(o => o.TotalPrice),
                    Cancelled = orders.Where(o => o.Status == OrderStatus.Cancelled).Sum(o => o.TotalPrice),
                    Returned = orders.Where(o => o.Status == OrderStatus.Returned).Sum(o => o.TotalPrice)
                }
            };

            return View(statistics);
        }
    }
}
