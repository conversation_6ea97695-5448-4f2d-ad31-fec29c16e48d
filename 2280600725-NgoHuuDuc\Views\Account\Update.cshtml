﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@model NgoHuuDuc_2280600725.Models.AccountViewModels.UserDetailsViewModel

@{
    ViewData["Title"] = "Cập nhật thông tin người dùng";
}

<h1>@ViewData["Title"]</h1>

<div class="row">
    <div class="col-md-8">
        <form asp-action="Update" method="post" enctype="multipart/form-data">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <input type="hidden" asp-for="AvatarUrl" />

            <div class="form-group mb-3">
                @if (!string.IsNullOrEmpty(Model.AvatarUrl))
                {
                    <div class="mb-2">
                        <img src="@Model.AvatarUrl" alt="Current Avatar" class="rounded-circle" style="width: 100px; height: 100px; object-fit: cover;" />
                    </div>
                }
                <label class="form-label">Thay đổi ảnh đại diện</label>
                <input type="file" name="AvatarFile" class="form-control" accept="image/*" />
            </div>

            <div class="form-group mb-3">
                <label asp-for="Email" class="form-label"></label>
                <input asp-for="Email" class="form-control" readonly />
            </div>

            <div class="form-group mb-3">
                <label asp-for="FullName" class="form-label"></label>
                <input asp-for="FullName" class="form-control" />
                <span asp-validation-for="FullName" class="text-danger"></span>
            </div>

            <div class="form-group mb-3">
                <label asp-for="DateOfBirth" class="form-label"></label>
                <input asp-for="DateOfBirth" type="date" class="form-control" />
                <span asp-validation-for="DateOfBirth" class="text-danger"></span>
            </div>

            <div class="form-group mb-3">
                <label asp-for="PhoneNumber" class="form-label"></label>
                <input asp-for="PhoneNumber" class="form-control" />
                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
            </div>

            <div class="form-group mb-3">
                <label asp-for="Address" class="form-label"></label>
                <input asp-for="Address" class="form-control" />
                <span asp-validation-for="Address" class="text-danger"></span>
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">Lưu thay đổi</button>
                @if (User.IsInRole("Administrator") && User.Identity?.Name != Model.Email)
                {
                    <a asp-action="UserDetails" asp-route-id="@Model.Id" class="btn btn-secondary">Quay lại</a>
                }
                else
                {
                    <a asp-action="Details" class="btn btn-secondary">Quay lại</a>
                }
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{ await Html.RenderPartialAsync("_ValidationScriptsPartial"); }
}

