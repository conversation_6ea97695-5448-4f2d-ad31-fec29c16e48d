@model NgoHuuDuc_2280600725.Models.ContactViewModel

@{
    ViewData["Title"] = "Liên hệ";
}

<div class="container mt-5">
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="contact-header text-center mb-5">
                <h1 class="display-4 fw-bold">Li<PERSON><PERSON> hệ <span class="text-elegant-gold">với chúng tôi</span></h1>
                <p class="lead">Chúng tôi luôn sẵn sàng lắng nghe và hỗ trợ bạn</p>
                <hr class="my-4 elegant-divider">
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            <div class="row">
                <div class="col-md-6 mb-5">
                    <div class="contact-info-card p-4 rounded-3 shadow-sm h-100">
                        <h2 class="section-title mb-4">Thông tin liên hệ</h2>

                        <div class="contact-item mb-4">
                            <h5><i class="fas fa-map-marker-alt me-2 text-elegant-gold"></i>Địa chỉ</h5>
                            <p class="ms-4">475A Điện Biên Phủ, Phường 25, Quận Bình Thạnh, TP. Hồ Chí Minh</p>
                        </div>

                        <div class="contact-item mb-4">
                            <h5><i class="fas fa-phone-alt me-2 text-elegant-gold"></i>Điện thoại</h5>
                            <p class="ms-4">
                                <a href="tel:+84123456789" class="contact-link">+84 123 456 789</a><br>
                                <a href="tel:+84987654321" class="contact-link">+84 987 654 321</a>
                            </p>
                        </div>

                        <div class="contact-item mb-4">
                            <h5><i class="fas fa-envelope me-2 text-elegant-gold"></i>Email</h5>
                            <p class="ms-4">
                                <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a><br>
                                <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
                            </p>
                        </div>

                        <div class="contact-item mb-4">
                            <h5><i class="fas fa-clock me-2 text-elegant-gold"></i>Giờ làm việc</h5>
                            <p class="ms-4">
                                Thứ Hai - Thứ Sáu: 9:00 - 21:00<br>
                                Thứ Bảy - Chủ Nhật: 9:00 - 18:00
                            </p>
                        </div>

                        <div class="contact-social mt-4">
                            <h5>Kết nối với chúng tôi</h5>
                            <div class="social-icons">
                                <a href="#" class="social-icon me-3"><i class="fab fa-facebook-f"></i></a>
                                <a href="#" class="social-icon me-3"><i class="fab fa-instagram"></i></a>
                                <a href="#" class="social-icon me-3"><i class="fab fa-twitter"></i></a>
                                <a href="#" class="social-icon"><i class="fab fa-youtube"></i></a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 mb-5">
                    <div class="contact-form-card p-4 rounded-3 shadow-sm h-100">
                        <h2 class="section-title mb-4">Gửi tin nhắn cho chúng tôi</h2>

                        <form asp-action="Contact" method="post">
                            <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                            <div class="mb-3">
                                <label asp-for="Name" class="form-label">Họ và tên</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input asp-for="Name" class="form-control" placeholder="Nhập họ và tên của bạn" />
                                </div>
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>

                            <div class="mb-3">
                                <label asp-for="Email" class="form-label">Email</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input asp-for="Email" class="form-control" placeholder="Nhập địa chỉ email của bạn" />
                                </div>
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>

                            <div class="mb-3">
                                <label asp-for="Phone" class="form-label">Số điện thoại</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                    <input asp-for="Phone" class="form-control" placeholder="Nhập số điện thoại của bạn" />
                                </div>
                                <span asp-validation-for="Phone" class="text-danger"></span>
                            </div>

                            <div class="mb-3">
                                <label asp-for="Subject" class="form-label">Tiêu đề</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-heading"></i></span>
                                    <input asp-for="Subject" class="form-control" placeholder="Nhập tiêu đề tin nhắn" />
                                </div>
                                <span asp-validation-for="Subject" class="text-danger"></span>
                            </div>

                            <div class="mb-3">
                                <label asp-for="ProductInterest" class="form-label">Sản phẩm quan tâm</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-tshirt"></i></span>
                                    <input asp-for="ProductInterest" class="form-control" placeholder="Nhập tên sản phẩm bạn quan tâm" value="@ViewBag.ProductName" />
                                </div>
                                <span asp-validation-for="ProductInterest" class="text-danger"></span>
                            </div>

                            <div class="mb-3">
                                <label asp-for="Message" class="form-label">Nội dung</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-comment"></i></span>
                                    <textarea asp-for="Message" class="form-control" rows="5" placeholder="Nhập nội dung tin nhắn của bạn"></textarea>
                                </div>
                                <span asp-validation-for="Message" class="text-danger"></span>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-elegant-primary">
                                    <i class="fas fa-paper-plane me-2"></i>Gửi tin nhắn
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="map-section mb-5">
                <h2 class="section-title mb-4 text-center">Bản đồ</h2>
                <div class="map-container rounded-3 shadow-sm">
                    <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.1258198091807!2d106.71419937465815!3d10.801628089348647!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x317528a459cb43ab%3A0x6c3d29d370b52a7e!2zxJDhuqFpIGjhu41jIEh1dGVjaA!5e0!3m2!1svi!2s!4v1710932862134!5m2!1svi!2s"
                            width="100%" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
