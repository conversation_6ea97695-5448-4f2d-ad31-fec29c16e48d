@model NgoHuuDuc_2280600725.Models.Order
@using NgoHuuDuc_2280600725.Models.Enums
@using NgoHuuDuc_2280600725.Extensions

@{
    ViewData["Title"] = "Chi tiết đơn hàng";
}

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>
            <i class="fas fa-file-invoice me-2" style="color: var(--elegant-gold);"></i>
            Chi tiết đơn hàng #@Model.Id
        </h1>
        <a asp-action="MyOrders" class="btn btn-elegant-secondary">
            <i class="fas fa-arrow-left me-1"></i> Quay lại danh sách
        </a>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card card-elegant mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Thông tin đơn hàng</h5>
                </div>
                <div class="card-body">                    <p><strong>Ngày đặt hàng:</strong> @Model.OrderDate.ToString("dd/MM/yyyy HH:mm")</p>
                    <p><strong>Tổng tiền:</strong> @Model.TotalPrice.ToString("N0")đ</p>
                    <p>
                        <strong>Trạng thái:</strong>
                        @{
                            var badgeClass = Model.Status switch
                            {
                                OrderStatus.Pending => "bg-warning",
                                OrderStatus.Confirmed => "bg-info",
                                OrderStatus.Shipping => "bg-primary",
                                OrderStatus.Delivered => "bg-success",
                                OrderStatus.Cancelled => "bg-danger",
                                OrderStatus.Returned => "bg-secondary",
                                _ => "bg-light"
                            };
                        }
                        <span class="badge @badgeClass">@Model.Status.GetDisplayName()</span>
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card card-elegant mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Thông tin giao hàng</h5>
                </div>
                <div class="card-body">
                    <p><strong>Địa chỉ giao hàng:</strong> @Model.ShippingAddress</p>
                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <p><strong>Ghi chú:</strong> @Model.Notes</p>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="card card-elegant">
        <div class="card-header">
            <h5 class="mb-0">Sản phẩm đã đặt</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Sản phẩm</th>
                            <th>Kích cỡ</th>
                            <th>Đơn giá</th>
                            <th>Số lượng</th>
                            <th>Thành tiền</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.OrderDetails != null)
                        {
                            @foreach (var item in Model.OrderDetails)
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if (item.Product != null && !string.IsNullOrEmpty(item.Product.ImageUrl))
                                            {
                                                <img src="@item.Product.ImageUrl" alt="@item.Product.Name" style="width:50px; height:50px; object-fit:cover" class="me-2" />
                                            }
                                            <span>@(item.Product?.Name ?? "Sản phẩm không còn tồn tại")</span>
                                        </div>
                                    </td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(item.Size))
                                        {
                                            <span class="badge bg-info">@item.Size</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>                                    <td>@item.Price.ToString("N0")đ</td>
                                    <td>@item.Quantity</td>
                                    <td>@((item.Price * item.Quantity).ToString("N0"))đ</td>
                                </tr>
                            }
                        }
                    </tbody>
                    <tfoot>
                        <tr>                            <td colspan="4" class="text-end"><strong>Tổng cộng:</strong></td>
                            <td><strong>@Model.TotalPrice.ToString("N0")đ</strong></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>
