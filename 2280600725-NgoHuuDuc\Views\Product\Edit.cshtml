﻿@model NgoHuuDuc_2280600725.Models.ViewModels.ProductViewModel
@using Microsoft.AspNetCore.Mvc.Rendering
@{
    ViewData["Title"] = "Sửa sản phẩm";
}

<h1><PERSON><PERSON>a</h1>

<h4><PERSON><PERSON><PERSON> phẩm</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit" enctype="multipart/form-data">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <input type="hidden" asp-for="ExistingImageUrl" />
            <input type="hidden" asp-for="ExistingModel3DUrl" />
            <div class="form-group mb-3">
                <label asp-for="Name" class="control-label"></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group mb-3">
                <label asp-for="Description" class="control-label"></label>
                @{
                    var description = Model.Description;
                    var sizeTag = "[SIZES]";
                    var endSizeTag = "[/SIZES]";
                    var reviewTag = "[REVIEWS]";
                    var endReviewTag = "[/REVIEWS]";

                    // Loại bỏ phần kỹ thuật khỏi mô tả
                    if (!string.IsNullOrEmpty(description))
                    {
                        // Loại bỏ phần kích thước
                        if (description.Contains(sizeTag) && description.Contains(endSizeTag))
                        {
                            var startIndex = description.IndexOf(sizeTag);
                            var endIndex = description.IndexOf(endSizeTag) + endSizeTag.Length;
                            if (startIndex < endIndex)
                            {
                                description = description.Remove(startIndex, endIndex - startIndex);
                            }
                        }

                        // Loại bỏ phần đánh giá
                        if (description.Contains(reviewTag) && description.Contains(endReviewTag))
                        {
                            var startIndex = description.IndexOf(reviewTag);
                            var endIndex = description.IndexOf(endReviewTag) + endReviewTag.Length;
                            if (startIndex < endIndex)
                            {
                                description = description.Remove(startIndex, endIndex - startIndex);
                            }
                        }

                        // Loại bỏ khoảng trắng thừa
                        description = description.Trim();
                    }
                }
                <textarea asp-for="Description" class="form-control" rows="3">@description</textarea>
                <span asp-validation-for="Description" class="text-danger"></span>
                <small class="form-text text-muted">Mô tả sản phẩm. Thông tin về kích thước sẽ được quản lý riêng.</small>
            </div>
            <div class="form-group mb-3">
                <label asp-for="Price" class="control-label"></label>
                <input asp-for="Price" class="form-control" />
                <span asp-validation-for="Price" class="text-danger"></span>
            </div>
            <div class="form-group mb-3">
                <label asp-for="Quantity" class="control-label"></label>
                <div class="input-group">
                    <input asp-for="Quantity" class="form-control" type="number" min="0" />
                    <a asp-action="ManageProductSizes" asp-route-id="@Model.Id" class="btn btn-outline-primary">
                        <i class="fas fa-tshirt"></i> Quản lý kích cỡ
                    </a>
                </div>
                <span asp-validation-for="Quantity" class="text-danger"></span>
                <small class="form-text text-muted">Số lượng tổng của tất cả các kích cỡ. Sử dụng nút "Quản lý kích cỡ" để thêm/sửa/xóa kích cỡ.</small>
            </div>
            <div class="form-group mb-3">
                <label asp-for="CategoryId" class="control-label"></label>
                <select asp-for="CategoryId" class="form-control" asp-items="@(new SelectList(Model.Categories, "Id", "Name"))">
                    <option value="">-- Chọn danh mục --</option>
                </select>
                <span asp-validation-for="CategoryId" class="text-danger"></span>
            </div>

            <div class="form-group mb-3">
                <div class="form-check">
                    <input asp-for="IsHidden" class="form-check-input" />
                    <label asp-for="IsHidden" class="form-check-label">Ẩn sản phẩm</label>
                </div>
                <small class="form-text text-muted">Khi được chọn, sản phẩm sẽ không hiển thị cho khách hàng.</small>
            </div>

            <div class="form-group mb-3">
                <label asp-for="Image" class="control-label"></label>
                <input asp-for="Image" type="file" class="form-control" accept="image/*" />
                <span asp-validation-for="Image" class="text-danger"></span>
            </div>

<div class="form-group mb-3">
    <label class="control-label">Hình ảnh hiện tại</label>
    <div>
        @if (!string.IsNullOrEmpty(Model.ExistingImageUrl))
        {
            <img src="@Model.ExistingImageUrl" alt="Hình ảnh hiện tại" style="max-width: 200px; max-height: 200px;" />
            <div class="d-flex justify-content-between align-items-center mt-2">
                <span class="text-muted">Tải lên hình ảnh mới để thay thế hình ảnh hiện tại</span>
                <a asp-action="DeleteImageGet" asp-route-id="@Model.Id" class="btn btn-danger btn-sm" onclick="return confirm('Bạn có chắc chắn muốn xóa hình ảnh này?');">
                    <i class="fas fa-trash-alt"></i> Xóa hình ảnh
                </a>
            </div>
        }
        else
        {
            <img src="/images/no-image.svg" alt="Không có hình ảnh" style="max-width: 200px; max-height: 200px;" onerror="this.src='https://via.placeholder.com/200x200?text=No+Image'" />
            <div class="mt-2">
                <span class="text-muted">Sản phẩm chưa có hình ảnh. Vui lòng tải lên hình ảnh.</span>
            </div>
        }
    </div>
</div>

            <div class="form-group mb-3">
                <label asp-for="Model3D" class="control-label">Mô hình 3D (không bắt buộc)</label>
                <input asp-for="Model3D" type="file" class="form-control" accept=".glb,.gltf,.obj" />
                <span asp-validation-for="Model3D" class="text-danger"></span>
            </div>

@if (!string.IsNullOrEmpty(Model.ExistingModel3DUrl))
{
    <div class="form-group mb-3">
        <label class="control-label">Mô hình 3D hiện tại</label>
        <div>
            <p><i class="fas fa-cube"></i> @System.IO.Path.GetFileName(Model.ExistingModel3DUrl)</p>
        </div>
        <div class="d-flex justify-content-between align-items-center mt-2">
            <span class="text-muted">Tải lên mô hình 3D mới để thay thế mô hình hiện tại</span>
            <a asp-action="DeleteModel3DGet" asp-route-id="@Model.Id" class="btn btn-danger btn-sm" onclick="return confirm('Bạn có chắc chắn muốn xóa mô hình 3D này?');">
                <i class="fas fa-trash-alt"></i> Xóa mô hình 3D
            </a>
        </div>
    </div>
}

            <div class="form-group">
                <button type="submit" class="btn btn-primary">Lưu</button>
                <a asp-action="Index" class="btn btn-secondary">Quay lại danh sách</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
