@model List<CartItem>

@if (Model.Any())
{
    <div class="order-summary">
        <ul class="list-group mb-3">
            @foreach (var item in Model)
            {
                <li class="list-group-item d-flex justify-content-between lh-sm">
                    <div>
                        <h6 class="my-0">@item.ProductName</h6>
                        <small class="text-muted">@item.Quantity x @item.Price.ToString("N0")đ</small>
                    </div>
                    <span class="text-muted">@((item.Price * item.Quantity).ToString("N0"))đ</span>
                </li>
            }
            <li class="list-group-item d-flex justify-content-between">
                <span>Tổng Cộng</span>
                <strong>@Model.Sum(i => i.Price * i.Quantity).ToString("N0")đ</strong>
            </li>
        </ul>
    </div>
}
else
{
    <div class="alert alert-warning">
        Giỏ hàng của bạn đang trống. Vui lòng thêm sản phẩm vào giỏ hàng trước khi thanh toán.
    </div>
}
