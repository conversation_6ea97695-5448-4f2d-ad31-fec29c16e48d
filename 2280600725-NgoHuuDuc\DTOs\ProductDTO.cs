using System.ComponentModel.DataAnnotations;

namespace NgoHuuDuc_2280600725.DTOs
{
    public class ProductDTO
    {
        public int Id { get; set; }

        public string Name { get; set; } = "";

        public string Description { get; set; } = "";

        public decimal Price { get; set; }

        public string ImageUrl { get; set; } = "";

        public string? Model3DUrl { get; set; }

        public int Quantity { get; set; } = 0;

        public bool IsHidden { get; set; } = false;

        public int CategoryId { get; set; }

        public string? CategoryName { get; set; }
    }

    public class CreateProductDTO
    {
        [Required(ErrorMessage = "Tên sản phẩm không được để trống")]
        [StringLength(200, ErrorMessage = "Tên sản phẩm không được vượt quá 200 ký tự")]
        public string Name { get; set; } = "";

        public string Description { get; set; } = "";

        [Required(ErrorMessage = "<PERSON><PERSON><PERSON> sản phẩm không được để trống")]
        [Range(0, double.MaxValue, ErrorMessage = "Gi<PERSON> sản phẩm phải lớn hơn hoặc bằng 0")]
        public decimal Price { get; set; }

        [Required(ErrorMessage = "Số lượng không được để trống")]
        [Range(0, int.MaxValue, ErrorMessage = "Số lượng phải lớn hơn hoặc bằng 0")]
        public int Quantity { get; set; } = 0;

        public bool IsHidden { get; set; } = false;

        [Required(ErrorMessage = "Danh mục không được để trống")]
        public int CategoryId { get; set; }

        public string? Model3DUrl { get; set; }
    }

    public class UpdateProductDTO
    {
        [Required(ErrorMessage = "Tên sản phẩm không được để trống")]
        [StringLength(200, ErrorMessage = "Tên sản phẩm không được vượt quá 200 ký tự")]
        public string Name { get; set; } = "";

        public string Description { get; set; } = "";

        [Required(ErrorMessage = "Giá sản phẩm không được để trống")]
        [Range(0, double.MaxValue, ErrorMessage = "Giá sản phẩm phải lớn hơn hoặc bằng 0")]
        public decimal Price { get; set; }

        [Required(ErrorMessage = "Số lượng không được để trống")]
        [Range(0, int.MaxValue, ErrorMessage = "Số lượng phải lớn hơn hoặc bằng 0")]
        public int Quantity { get; set; } = 0;



        [Required(ErrorMessage = "Danh mục không được để trống")]
        public int CategoryId { get; set; }

        public string? Model3DUrl { get; set; }
    }
}
