﻿@model NgoHuuDuc_2280600725.Models.AccountViewModels.RegisterViewModel

@{
    ViewData["Title"] = "Đăng ký";
}

<h1>@ViewData["Title"]</h1>

<div class="row">
    <div class="col-md-6">
        <form asp-controller="Account" asp-action="Register" method="post" enctype="multipart/form-data">
            <h4>Tạo tài k<PERSON>ản mới</h4>
            <hr />
            <div asp-validation-summary="All" class="text-danger"></div>

            <div class="form-group mb-3">
                <label asp-for="AvatarFile" class="form-label"></label>
                <input asp-for="AvatarFile" type="file" class="form-control" accept="image/*" />
                <span asp-validation-for="AvatarFile" class="text-danger"></span>
            </div>

            <div class="form-group mb-3">
                <label asp-for="Email" class="form-label"></label>
                <input asp-for="Email" class="form-control" />
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>

            <div class="form-group mb-3">
                <label asp-for="FullName" class="form-label"></label>
                <input asp-for="FullName" class="form-control" />
                <span asp-validation-for="FullName" class="text-danger"></span>
            </div>

            <div class="form-group mb-3">
                <label asp-for="Password" class="form-label"></label>
                <input asp-for="Password" class="form-control" />
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>

            <div class="form-group mb-3">
                <label asp-for="ConfirmPassword" class="form-label"></label>
                <input asp-for="ConfirmPassword" class="form-control" />
                <span asp-validation-for="ConfirmPassword" class="text-danger" id="confirmPasswordError"></span>
            </div>

            <div class="form-group mb-3">
                <label asp-for="DateOfBirth" class="form-label"></label>
                <input asp-for="DateOfBirth" class="form-control" type="date" />
                <span asp-validation-for="DateOfBirth" class="text-danger"></span>
            </div>

            <div class="form-group mb-3">
                <label asp-for="PhoneNumber" class="form-label"></label>
                <input asp-for="PhoneNumber" class="form-control" />
                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
            </div>

            <div class="form-group mb-3">
                <label asp-for="Address" class="form-label"></label>
                <input asp-for="Address" class="form-control" />
                <span asp-validation-for="Address" class="text-danger"></span>
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">Đăng ký</button>
                <a asp-action="Login" class="btn btn-secondary">Đã có tài khoản?</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{ await Html.RenderPartialAsync("_ValidationScriptsPartial"); }

    <script>
        // Thêm xác thực client-side cho mật khẩu xác nhận
        $(document).ready(function() {
            // Kiểm tra mật khẩu xác nhận khi người dùng nhập
            $("#ConfirmPassword").on("input", function() {
                var password = $("#Password").val();
                var confirmPassword = $(this).val();

                if (password !== confirmPassword) {
                    // Hiển thị lỗi nếu mật khẩu không khớp
                    $(this).addClass("is-invalid");
                    $("#confirmPasswordError").text("Mật khẩu xác nhận không khớp với mật khẩu đã nhập.");
                    $("#confirmPasswordError").show();
                } else {
                    // Xóa lỗi nếu mật khẩu khớp
                    $(this).removeClass("is-invalid");
                    $("#confirmPasswordError").text("");
                    $("#confirmPasswordError").hide();
                }
            });

            // Kiểm tra lại khi mật khẩu thay đổi
            $("#Password").on("input", function() {
                // Kích hoạt sự kiện input trên ConfirmPassword để kiểm tra lại
                $("#ConfirmPassword").trigger("input");
            });

            // Kiểm tra trước khi submit form
            $("form").on("submit", function(e) {
                var password = $("#Password").val();
                var confirmPassword = $("#ConfirmPassword").val();

                if (password !== confirmPassword) {
                    e.preventDefault(); // Ngăn form submit
                    $("#ConfirmPassword").addClass("is-invalid");
                    $("#confirmPasswordError").text("Mật khẩu xác nhận không khớp với mật khẩu đã nhập.");
                    $("#confirmPasswordError").show();

                    // Cuộn đến vị trí lỗi
                    $('html, body').animate({
                        scrollTop: $("#ConfirmPassword").offset().top - 100
                    }, 200);

                    return false;
                }

                return true;
            });
        });
    </script>
}