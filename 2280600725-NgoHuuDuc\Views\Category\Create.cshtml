﻿@model NgoHuuDuc_2280600725.Models.Category
@using Microsoft.AspNetCore.Mvc.Rendering
@{
    ViewData["Title"] = "Tạo danh mục";
}

<h1>Tạo mới danh mục</h1>

<div class="row">
    <div class="col-md-4">
        <div asp-validation-summary="All" class="text-danger"></div>
        <form asp-controller="Category" asp-action="Create" method="post">
            <div class="form-group mb-3">
                <label asp-for="Name">Tên danh mục</label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group mb-3">
                <label asp-for="Description">Mô tả</label>
                <input asp-for="Description" class="form-control" />
                <span asp-validation-for="Description" class="text-danger"></span>
            </div>
            <button type="submit" class="btn btn-primary">Tạo mới</button>
            <a asp-action="Index" class="btn btn-secondary">Quay lại</a>
        </form>
    </div>
</div>

@section Scripts {
@{
    await Html.RenderPartialAsync("_ValidationScriptsPartial");
}
}
