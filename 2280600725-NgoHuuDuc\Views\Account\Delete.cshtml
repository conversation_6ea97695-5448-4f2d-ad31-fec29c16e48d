﻿@model NgoHuuDuc_2280600725.Models.AccountViewModels.UserDetailsViewModel

@{
    ViewData["Title"] = "Xóa người dùng";
}

<h1>@ViewData["Title"]</h1>

<div class="alert alert-danger">
    <h3>Bạn có chắc chắn muốn xóa người dùng này?</h3>
</div>

<div class="row">
    <div class="col-md-6">
        @if (!string.IsNullOrEmpty(Model.AvatarUrl))
        {
            <div class="mb-3">
                <img src="@Model.AvatarUrl" alt="Avatar" class="rounded-circle" style="width: 100px; height: 100px; object-fit: cover;" />
            </div>
        }
        
        <dl class="row">
            <dt class="col-sm-4">Email</dt>
            <dd class="col-sm-8">@Model.Email</dd>

            <dt class="col-sm-4">Họ và tên</dt>
            <dd class="col-sm-8">@Model.FullName</dd>

            <dt class="col-sm-4">Số điện thoại</dt>
            <dd class="col-sm-8">@(Model.PhoneNumber ?? "Chưa cập nhật")</dd>
            
            <dt class="col-sm-4">Địa chỉ</dt>
            <dd class="col-sm-8">@(Model.Address ?? "Chưa cập nhật")</dd>
        </dl>

        <form asp-action="Delete" method="post">
            <input type="hidden" asp-for="Id" />
            <div class="form-group">
                <button type="submit" class="btn btn-danger">Xác nhận xóa</button>
                <a asp-action="Index" class="btn btn-secondary">Quay lại</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
