﻿@using NgoHuuDuc_2280600725.Models.AccountViewModels
@model IEnumerable<NgoHuuDuc_2280600725.Models.AccountViewModels.UserDetailsViewModel>

@{
    ViewData["Title"] = "Quản lý người dùng & vai trò";
}

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show">
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show">
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["Message"] != null)
{
    <div class="alert alert-success alert-dismissible fade show">
        @TempData["Message"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["Error"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show">
        @TempData["Error"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<div class="dashboard-card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <span><i class="fas fa-users me-2"></i>@ViewData["Title"]</span>
        <button id="saveRolesBtn" class="btn btn-elegant-primary btn-sm" style="display: none;">
            <i class="fas fa-save me-1"></i> Lưu thay đổi vai trò
        </button>
    </div>
    <div class="card-body">

<div class="table-responsive">
    <table class="dashboard-table">
    <thead>
        <tr>
            <th>Ảnh</th>
            <th>Họ và tên</th>
            <th>Email</th>
            <th>Số điện thoại</th>
            <th>Trạng thái</th>
            <th>Vai trò</th>
            <th>Thao tác</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var user in Model)
        {
            <tr>
                <td>
                    @if (!string.IsNullOrEmpty(user.AvatarUrl))
                    {
                        <img src="@user.AvatarUrl" alt="Avatar" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;" />
                    }
                </td>
                <td>@user.FullName</td>
                <td>@user.Email</td>
                <td>@(user.PhoneNumber ?? "Chưa cập nhật")</td>
                <td>
                    <span class="badge @(user.IsActive ? "bg-success" : "bg-danger")">
                        @(user.IsActive ? "Hoạt động" : "Đã khóa")
                    </span>
                </td>
                <td>
                    <select class="form-select form-select-sm role-select" data-user-id="@user.Id" @(user.Email == User.Identity.Name ? "disabled" : "")>
                        <option value="loading">Loading...</option>
                    </select>
                </td>
                <td>
                    <div class="btn-group">
                        <a asp-action="UserDetails" asp-route-id="@user.Id" class="btn btn-sm btn-elegant-secondary">
                            <i class="fas fa-info-circle me-1"></i> Chi tiết
                        </a>
                        @if (User.IsInRole("Administrator") && user.Email != User.Identity.Name)
                        {
                            @if (user.IsActive)
                            {
                                <form asp-action="LockUser" method="post" style="display: inline;">
                                    <input type="hidden" name="id" value="@user.Id" />
                                    <button type="submit" class="btn btn-sm btn-warning" onclick="return confirm('Bạn có chắc chắn muốn khóa người dùng này?')">
                                        <i class="fas fa-lock me-1"></i> Khóa
                                    </button>
                                </form>
                            }
                            else
                            {
                                <form asp-action="UnlockUser" method="post" style="display: inline;">
                                    <input type="hidden" name="id" value="@user.Id" />
                                    <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('Bạn có chắc chắn muốn mở khóa người dùng này?')">
                                        <i class="fas fa-unlock me-1"></i> Mở khóa
                                    </button>
                                </form>
                            }
                            <a asp-action="Delete" asp-route-id="@user.Id" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash-alt me-1"></i> Xóa
                            </a>
                        }
                    </div>
                </td>
            </tr>
        }
    </tbody>
    </table>
</div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Lưu trữ vai trò ban đầu và vai trò đã thay đổi
            var originalRoles = {};
            var changedRoles = {};
            var allRoles = [];

            // Lấy danh sách tất cả vai trò
            $.ajax({
                url: '/Account/GetAllRoles',
                type: 'GET',
                success: function(roles) {
                    allRoles = roles;

                    // Lấy vai trò cho từng người dùng
                    $('.role-select').each(function() {
                        var userId = $(this).data('user-id');
                        var select = $(this);

                        $.ajax({
                            url: '/Account/GetUserRoles',
                            type: 'GET',
                            data: { userId: userId },
                            success: function(userRoles) {
                                // Lưu vai trò ban đầu
                                originalRoles[userId] = userRoles;

                                // Xóa option loading
                                select.empty();

                                // Thêm các option vai trò
                                $.each(allRoles, function(i, role) {
                                    var isSelected = userRoles.includes(role);
                                    select.append($('<option>', {
                                        value: role,
                                        text: role,
                                        selected: isSelected
                                    }));
                                });

                                // Chuyển select thành multiple select với Select2
                                select.select2({
                                    placeholder: 'Chọn vai trò',
                                    allowClear: true,
                                    width: '100%'
                                });

                                // Xử lý sự kiện thay đổi
                                select.on('change', function() {
                                    var userId = $(this).data('user-id');
                                    var selectedRoles = $(this).val();

                                    // Kiểm tra nếu người dùng hiện tại là admin và đang cố gắng xóa vai trò admin
                                    if ($(this).is(':disabled')) {
                                        return;
                                    }

                                    // Đảm bảo selectedRoles là một mảng
                                    if (selectedRoles === null) {
                                        selectedRoles = [];
                                    }

                                    // Lưu vai trò đã thay đổi
                                    changedRoles[userId] = selectedRoles;
                                    console.log('Changed roles for user ' + userId + ':', selectedRoles);

                                    // Hiển thị nút lưu
                                    $('#saveRolesBtn').show();
                                });
                            }
                        });
                    });
                }
            });

            // Xử lý sự kiện lưu vai trò
            $('#saveRolesBtn').click(function() {
                // Kiểm tra xem có vai trò nào đã thay đổi không
                if (Object.keys(changedRoles).length === 0) {
                    alert('Không có thay đổi nào để lưu');
                    return;
                }

                // Hiển thị thông báo đang xử lý
                $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i> Đang lưu...');

                console.log('Sending data:', changedRoles);

                // Chuyển đổi changedRoles thành định dạng phù hợp
                var dataToSend = {};
                $.each(changedRoles, function(userId, roles) {
                    // Đảm bảo roles là một mảng
                    if (roles === null) {
                        roles = [];
                    } else if (!Array.isArray(roles)) {
                        roles = [roles];
                    }
                    dataToSend[userId] = roles;
                });

                console.log('Data to send:', dataToSend);

                // Gửi dữ liệu vai trò đã thay đổi lên server
                $.ajax({
                    url: '/Account/UpdateRoles',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(dataToSend),
                    success: function(response) {
                        if (response.success) {
                            // Hiển thị thông báo thành công
                            $('<div class="alert alert-success alert-dismissible fade show">' +
                              'Cập nhật vai trò thành công' +
                              '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                              '</div>').insertAfter('.mb-4').alert();

                            // Cập nhật vai trò ban đầu
                            $.each(changedRoles, function(userId, roles) {
                                originalRoles[userId] = roles;
                            });

                            // Xóa vai trò đã thay đổi
                            changedRoles = {};

                            // Ẩn nút lưu
                            $('#saveRolesBtn').hide();
                        } else {
                            // Hiển thị thông báo lỗi
                            $('<div class="alert alert-danger alert-dismissible fade show">' +
                              'Lỗi: ' + response.message +
                              '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                              '</div>').insertAfter('.mb-4').alert();
                        }
                    },
                    error: function() {
                        // Hiển thị thông báo lỗi
                        $('<div class="alert alert-danger alert-dismissible fade show">' +
                          'Đã xảy ra lỗi khi cập nhật vai trò' +
                          '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                          '</div>').insertAfter('.mb-4').alert();
                    },
                    complete: function() {
                        // Khôi phục nút lưu
                        $('#saveRolesBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> Lưu thay đổi vai trò');
                    }
                });
            });
        });
    </script>
}
