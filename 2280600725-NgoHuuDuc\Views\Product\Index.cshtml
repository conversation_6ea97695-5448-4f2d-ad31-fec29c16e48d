@model NgoHuuDuc_2280600725.Helpers.PaginatedList<NgoHuuDuc_2280600725.Models.Product>
@{
    ViewData["Title"] = "Sản phẩm";
}

<div class="row">
    <div class="col-md-2">
        <div class="dashboard-card mb-4">
            <div class="card-header">
                <span><i class="fas fa-list me-2"></i><PERSON><PERSON> mục sản phẩm</span>
            </div>
            <div class="card-body p-0">
                <div class="list-group category-list-elegant">
                    @{
                        var sortParams = new Dictionary<string, string>();
                        if (!string.IsNullOrEmpty(ViewBag.SortBy?.ToString()))
                        {
                            sortParams.Add("sortBy", ViewBag.SortBy.ToString());
                        }
                        if (!string.IsNullOrEmpty(ViewBag.Order?.ToString()))
                        {
                            sortParams.Add("order", ViewBag.Order.ToString());
                        }
                    }

                    <a href="@Url.Action("Index", "Product", sortParams)" class="list-group-item list-group-item-action @(ViewBag.SelectedCategory == null ? "active" : "")">
                        <i class="fas fa-tags me-2"></i>Tất cả danh mục
                    </a>
                    @if (ViewBag.Categories != null)
                    {
                        foreach (var category in ViewBag.Categories)
                        {
                            var categoryParams = new Dictionary<string, string>(sortParams);
                            categoryParams.Add("categoryId", category.Id.ToString());

                            <a href="@Url.Action("Index", "Product", categoryParams)"
                               class="list-group-item list-group-item-action @(ViewBag.SelectedCategory?.Id == category.Id ? "active" : "")">
                                <i class="fas fa-tag me-2"></i>@category.Name
                            </a>
                        }
                    }
                </div>
            </div>
        </div>

        @if (User.IsInRole("Administrator"))
        {
            <div class="dashboard-card mb-4">
                <div class="card-header">
                    <span><i class="fas fa-cog me-2"></i>Thao tác</span>
                </div>
                <div class="card-body">
                    <a asp-action="Create" class="btn btn-elegant-primary w-100">
                        <i class="fas fa-plus-circle me-2"></i>Thêm sản phẩm mới
                    </a>
                </div>
            </div>
        }
    </div>
    <div class="col-md-10">
        <div class="dashboard-card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>
                        <i class="fas fa-tshirt me-2"></i>
                        @(ViewBag.SelectedCategory == null ? "Tất cả sản phẩm" : ViewBag.SelectedCategory?.Name ?? "Danh mục không xác định")
                    </span>
                    <span class="badge bg-primary">@(ViewBag.TotalItems ?? 0) sản phẩm</span>
                </div>

                <!-- Sorting Controls for All Users -->
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <label class="form-label me-2 mb-0">Sắp xếp theo:</label>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" id="sortDropdown">
                                <i class="fas fa-sort me-1"></i>
                                <span id="currentSortText">
                                    @{
                                        var currentSort = ViewBag.CurrentSort?.ToString() ?? "";
                                        var sortText = currentSort switch
                                        {
                                            "price-asc" => "Giá: Thấp đến Cao",
                                            "price-desc" => "Giá: Cao đến Thấp",
                                            "name-asc" => "Tên: A đến Z",
                                            "name-desc" => "Tên: Z đến A",
                                            _ => "Mặc định"
                                        };
                                    }
                                    @sortText
                                </span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item sort-option @(ViewBag.CurrentSort == "name-asc" ? "active" : "")" href="#" data-sort="name" data-order="asc">
                                    <i class="fas fa-sort-alpha-down me-2"></i>Tên: A đến Z
                                </a></li>
                                <li><a class="dropdown-item sort-option @(ViewBag.CurrentSort == "name-desc" ? "active" : "")" href="#" data-sort="name" data-order="desc">
                                    <i class="fas fa-sort-alpha-up me-2"></i>Tên: Z đến A
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item sort-option @(ViewBag.CurrentSort == "price-asc" ? "active" : "")" href="#" data-sort="price" data-order="asc">
                                    <i class="fas fa-sort-numeric-down me-2"></i>Giá: Thấp đến Cao
                                </a></li>
                                <li><a class="dropdown-item sort-option @(ViewBag.CurrentSort == "price-desc" ? "active" : "")" href="#" data-sort="price" data-order="desc">
                                    <i class="fas fa-sort-numeric-up me-2"></i>Giá: Cao đến Thấp
                                </a></li>
                            </ul>
                        </div>
                    </div>

                    @if (!User.IsInRole("Administrator"))
                    {
                        <div class="d-flex align-items-center">
                            <div class="input-group input-group-sm" style="width: 250px;">
                                <input type="text" id="searchInput" class="form-control" placeholder="Tìm kiếm sản phẩm..." value="@ViewBag.SearchKeyword">
                                <button class="btn btn-elegant-primary" type="button" id="searchBtn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    }
                </div>
            </div>
            <div class="card-body">
                @* Display TempData messages *@
                @if (TempData["Success"] != null)
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>@TempData["Success"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                }
                @if (TempData["Error"] != null)
                {
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>@TempData["Error"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                }
                @if (TempData["Warning"] != null)
                {
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>@TempData["Warning"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                }
                @if (TempData["Info"] != null)
                {
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>@TempData["Info"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                }

                @if (User.IsInRole("Administrator"))
                {
                    <div class="mb-3 d-flex justify-content-between align-items-center">
                        <div>
                            <button id="selectAllBtn" class="btn btn-sm btn-outline-secondary me-2">
                                <i class="fas fa-check-square me-1"></i>Chọn tất cả
                            </button>
                            <button id="deselectAllBtn" class="btn btn-sm btn-outline-secondary me-2">
                                <i class="fas fa-square me-1"></i>Bỏ chọn tất cả
                            </button>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-elegant-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-cogs me-1"></i>Thao tác hàng loạt
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="#" id="bulkDeleteBtn">
                                            <i class="fas fa-trash-alt me-1 text-danger"></i>Xóa đã chọn
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" id="bulkUpdateStockBtn">
                                            <i class="fas fa-boxes me-1 text-primary"></i>Cập nhật tồn kho
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="btn-group ms-2">
                                <button type="button" class="btn btn-sm btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-file-excel me-1"></i>Excel
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="#" id="bulkExportBtn">
                                            <i class="fas fa-file-export me-1 text-success"></i>Xuất Excel
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" id="importExcelBtn">
                                            <i class="fas fa-file-import me-1 text-primary"></i>Nhập từ Excel
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="/Excel/DownloadTemplate" target="_blank">
                                            <i class="fas fa-download me-1 text-info"></i>Tải mẫu Excel
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="input-group input-group-sm me-2" style="width: 250px;">
                                <input type="text" id="searchInput" class="form-control" placeholder="Tìm kiếm sản phẩm...">
                                <button class="btn btn-elegant-primary" type="button" id="searchBtn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-sort me-1"></i>Sắp xếp
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item sort-option" href="#" data-sort="name-asc">Tên A-Z</a></li>
                                    <li><a class="dropdown-item sort-option" href="#" data-sort="name-desc">Tên Z-A</a></li>
                                    <li><a class="dropdown-item sort-option" href="#" data-sort="price-asc">Giá tăng dần</a></li>
                                    <li><a class="dropdown-item sort-option" href="#" data-sort="price-desc">Giá giảm dần</a></li>
                                    <li><a class="dropdown-item sort-option" href="#" data-sort="stock-asc">Tồn kho tăng dần</a></li>
                                    <li><a class="dropdown-item sort-option" href="#" data-sort="stock-desc">Tồn kho giảm dần</a></li>
                                </ul>
                            </div>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-filter me-1"></i>Lọc
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item filter-option" href="#" data-filter="all">Tất cả sản phẩm</a></li>
                                    <li><a class="dropdown-item filter-option" href="#" data-filter="in-stock">Còn hàng</a></li>
                                    <li><a class="dropdown-item filter-option" href="#" data-filter="out-of-stock">Hết hàng</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <form id="bulkActionForm" method="post">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped align-middle">
                                <thead class="table-light">
                                    <tr>
                                        <th width="40px">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAll">
                                            </div>
                                        </th>
                                        <th width="60px">ID</th>
                                        <th width="80px">Hình ảnh</th>
                                        <th>Tên sản phẩm</th>
                                        <th>Danh mục</th>
                                        <th>Giá</th>
                                        <th>Tồn kho</th>
                                        <th>Trạng thái</th>
                                        <th width="150px">Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (!Model.Any())
                                    {
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <div class="alert alert-info mb-0">
                                                    <i class="fas fa-info-circle me-2"></i>Không có sản phẩm nào trong danh mục này.
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                    else
                                    {
                                        int startIndex = ((ViewBag.CurrentPage ?? 1) - 1) * (ViewBag.PageSize ?? 12) + 1;

                                        for (int i = 0; i < Model.Count; i++)
                                        {
                                            var item = Model[i];
                                            int productNumber = startIndex + i;
                                            <tr>
                                                <td>
                                                    <div class="form-check">
                                                        <input class="form-check-input product-checkbox" type="checkbox" name="selectedProducts" value="@item.Id">
                                                    </div>
                                                </td>
                                                <td>@item.Id</td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(item.ImageUrl))
                                                    {
                                                        <img src="@item.ImageUrl" class="img-thumbnail" alt="@item.Name" style="width: 60px; height: 60px; object-fit: cover;">
                                                    }
                                                    else
                                                    {
                                                        <div class="bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                                            <i class="fas fa-image"></i>
                                                        </div>
                                                    }
                                                </td>
                                                <td>
                                                    <a href="@Url.Action("Details", new { id = item.Id })" class="text-decoration-none fw-bold product-name">
                                                        @item.Name
                                                    </a>
                                                    @if (!string.IsNullOrEmpty(item.Model3DUrl))
                                                    {
                                                        <span class="badge bg-info ms-1" title="Có mô hình 3D">
                                                            <i class="fas fa-cube"></i>
                                                        </span>
                                                    }
                                                </td>
                                                <td>
                                                    <span class="badge bg-elegant-secondary">@item.Category.Name</span>
                                                </td>
                                                <td class="product-price">@item.Price.ToString("N0") VNĐ</td>
                                                <td>
                                                    <span class="badge bg-@(item.Quantity > 0 ? "success" : "danger") product-stock">
                                                        @(item.Quantity > 0 ? item.Quantity.ToString() : "Hết hàng")
                                                    </span>
                                                </td>
                                                <td>
                                                    @if (item.IsHidden)
                                                    {
                                                        <span class="badge bg-warning text-dark">
                                                            <i class="fas fa-eye-slash me-1"></i>Đã ẩn
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-info">
                                                            <i class="fas fa-eye me-1"></i>Hiển thị
                                                        </span>
                                                    }
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-elegant-primary" title="Sửa">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-elegant-secondary" title="Chi tiết">
                                                            <i class="fas fa-info-circle"></i>
                                                        </a>
                                                        @if (item.Quantity > 0)
                                                        {
                                                            <button type="button" class="btn btn-secondary" disabled title="Không thể xóa sản phẩm còn hàng. Vui lòng ẩn sản phẩm hoặc cập nhật tồn kho về 0 trước khi xóa.">
                                                                <i class="fas fa-trash-alt"></i>
                                                            </button>
                                                        }
                                                        else
                                                        {
                                                            <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-danger" title="Xóa sản phẩm">
                                                                <i class="fas fa-trash-alt"></i>
                                                            </a>
                                                        }
                                                        <button type="button" class="btn btn-success quick-edit-btn"
                                                                data-id="@item.Id"
                                                                data-name="@item.Name"
                                                                data-price="@item.Price"
                                                                data-stock="@item.Quantity"
                                                                title="Sửa nhanh">
                                                            <i class="fas fa-bolt"></i>
                                                        </button>
                                                        <button type="button" class="btn @(item.IsHidden ? "btn-info" : "btn-warning") toggle-visibility-btn"
                                                                data-id="@item.Id"
                                                                data-is-hidden="@item.IsHidden.ToString().ToLower()"
                                                                title="@(item.IsHidden ? "Hiển thị sản phẩm" : "Ẩn sản phẩm")">
                                                            <i class="fas @(item.IsHidden ? "fa-eye" : "fa-eye-slash")"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </form>
                }
                else
                {
                    <div class="row">
                    @{
                        int startIndex = ((ViewBag.CurrentPage ?? 1) - 1) * (ViewBag.PageSize ?? 12) + 1;
                    }

                    @for (int i = 0; i < Model.Count; i++)
                    {
                        var item = Model[i];
                        int productNumber = startIndex + i;
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 card-elegant">
                                @if (!string.IsNullOrEmpty(item.ImageUrl))
                                {
                                    <img src="@item.ImageUrl" class="card-img-top" alt="@item.Name" style="height: 250px; object-fit: cover;">
                                }
                                else
                                {
                                    <div class="card-img-top bg-secondary text-white d-flex align-items-center justify-content-center" style="height: 250px;">
                                        <span><i class="fas fa-image me-2"></i>Không có hình ảnh</span>
                                    </div>
                                }
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <span class="badge bg-elegant-secondary me-1">#@productNumber</span>
                                        @item.Name
                                    </h5>
                                    <p class="card-text text-muted">
                                        <i class="fas fa-tag me-1"></i>@item.Category.Name
                                    </p>
                                    @{
                                        var description = item.Description;
                                        var sizeTag = "[SIZES]";
                                        var endSizeTag = "[/SIZES]";
                                        var reviewTag = "[REVIEWS]";
                                        var endReviewTag = "[/REVIEWS]";

                                        // Loại bỏ phần kỹ thuật khỏi mô tả
                                        if (!string.IsNullOrEmpty(description))
                                        {
                                            // Loại bỏ phần kích thước
                                            if (description.Contains(sizeTag) && description.Contains(endSizeTag))
                                            {
                                                var sizeStartIndex = description.IndexOf(sizeTag);
                                                var sizeEndIndex = description.IndexOf(endSizeTag) + endSizeTag.Length;
                                                if (sizeStartIndex < sizeEndIndex)
                                                {
                                                    description = description.Remove(sizeStartIndex, sizeEndIndex - sizeStartIndex);
                                                }
                                            }

                                            // Loại bỏ phần đánh giá
                                            if (description.Contains(reviewTag) && description.Contains(endReviewTag))
                                            {
                                                var reviewStartIndex = description.IndexOf(reviewTag);
                                                var reviewEndIndex = description.IndexOf(endReviewTag) + endReviewTag.Length;
                                                if (reviewStartIndex < reviewEndIndex)
                                                {
                                                    description = description.Remove(reviewStartIndex, reviewEndIndex - reviewStartIndex);
                                                }
                                            }

                                            // Loại bỏ khoảng trắng thừa
                                            description = description.Trim();
                                        }

                                        // Cắt ngắn mô tả nếu quá dài
                                        var displayDescription = description?.Length > 100
                                            ? description.Substring(0, 100) + "..."
                                            : description;
                                    }
                                    <p class="card-text">@displayDescription</p>
                                    <p class="card-text price">@item.Price.ToString("N0") VNĐ</p>
                                    <p class="card-text">
                                        Tình trạng:
                                        <span class="badge bg-@(item.Quantity > 0 ? "success" : "danger")">
                                            @(item.Quantity > 0 ? "Còn hàng" : "Hết hàng")
                                        </span>
                                    </p>
                                </div>
                                <div class="card-footer bg-transparent">
                                    <div class="d-flex justify-content-between">
                                        <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-elegant-secondary">
                                            <i class="fas fa-info-circle me-1"></i>Chi tiết
                                        </a>
                                        @if (User.Identity.IsAuthenticated)
                                        {
                                            @if (item.Quantity > 0)
                                            {
                                                <a href="#" class="btn btn-elegant-primary add-to-cart" data-product-id="@item.Id">
                                                    <i class="fas fa-cart-plus me-1"></i>Thêm vào giỏ
                                                </a>
                                            }
                                            else
                                            {
                                                <a asp-controller="Home" asp-action="Contact" asp-route-productId="@item.Id" class="btn btn-elegant-primary">
                                                    <i class="fas fa-envelope me-1"></i>Liên hệ đặt hàng
                                                </a>
                                            }
                                        }
                                        else
                                        {
                                            <a asp-controller="Account" asp-action="Login" asp-route-returnUrl="@Url.Action("Details", "Product", new { id = item.Id })" class="btn btn-elegant-primary">
                                                <i class="fas fa-sign-in-alt me-1"></i>Đăng nhập để mua
                                            </a>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    </div>
                }

                <!-- Pagination -->
                <div class="mt-4">
                    @await Html.PartialAsync("_Pagination")
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .sort-option.active {
            background-color: #007bff;
            color: white;
        }

        .sort-option.active i {
            color: white;
        }

        .dropdown-menu .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-menu .dropdown-item.active:hover {
            background-color: #0056b3;
        }

        /* Disabled delete button styling */
        .btn-group .btn[disabled] {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-group .btn[disabled]:hover {
            opacity: 0.5;
        }
    </style>
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Sorting functionality
            $('.sort-option').click(function(e) {
                e.preventDefault();
                var sortBy = $(this).data('sort');
                var order = $(this).data('order');
                var categoryId = '@ViewBag.CategoryId';

                // Build URL with sorting parameters
                var url = '@Url.Action("Index", "Product")';
                var params = [];

                if (categoryId && categoryId !== '') {
                    params.push('categoryId=' + categoryId);
                }
                params.push('sortBy=' + sortBy);
                params.push('order=' + order);

                if (params.length > 0) {
                    url += '?' + params.join('&');
                }

                // Navigate to sorted results
                window.location.href = url;
            });

            // Search functionality
            $('#searchBtn').click(function() {
                var keyword = $('#searchInput').val().trim();
                if (keyword) {
                    var url = '@Url.Action("Search", "Product")' + '?keyword=' + encodeURIComponent(keyword);
                    window.location.href = url;
                } else {
                    window.location.href = '@Url.Action("Index", "Product")';
                }
            });

            // Search on Enter key
            $('#searchInput').keypress(function(e) {
                if (e.which === 13) {
                    $('#searchBtn').click();
                }
            });

            // Initialize Bootstrap tooltips for disabled delete buttons
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"], [title]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });

            // Add hover effect to product cards
            $('.card-elegant').hover(function() {
                $(this).addClass('shadow-lg').css('cursor', 'pointer');
            }, function() {
                $(this).removeClass('shadow-lg');
            });

            // Add to cart functionality
            $('.add-to-cart').click(function(e) {
                e.preventDefault();
                var productId = $(this).data('product-id');

                $.ajax({
                    url: '/Home/AddToCart',
                    type: 'POST',
                    data: { productId: productId },
                    success: function(result) {
                        if (result.success) {
                            updateCartCount();
                            alert('Sản phẩm đã được thêm vào giỏ hàng!');
                        } else {
                            alert(result.message || 'Sản phẩm đã hết hàng, bạn có thể liên hệ để đặt hàng!');
                            // Cập nhật UI nếu sản phẩm hết hàng
                            var $button = $('.add-to-cart[data-product-id="' + productId + '"]');
                            $button.replaceWith('<a href="/Home/Contact?productId=' + productId + '" class="btn btn-primary"><i class="fas fa-envelope me-1"></i>Liên hệ đặt hàng</a>');
                        }
                    },
                    error: function() {
                        alert('Có lỗi xảy ra khi thêm sản phẩm vào giỏ hàng!');
                    }
                });
            });

            // Highlight active category with a pulse effect
            if ($('.category-list-elegant .list-group-item.active').length > 0) {
                $('.category-list-elegant .list-group-item.active').addClass('gold-pulse');
            }

            // Add click effect to category items
            $('.category-list-elegant .list-group-item').click(function() {
                // Store the href for navigation
                var href = $(this).attr('href');

                // Add a brief highlight effect before navigating
                $(this).addClass('clicked');

                // Navigate after a short delay to show the effect
                setTimeout(function() {
                    window.location.href = href;
                }, 150);

                return false; // Prevent default navigation
            });

            // Admin table functionality
            if ($('#selectAll').length > 0) {
                // Select all checkbox
                $('#selectAll').change(function() {
                    $('.product-checkbox').prop('checked', $(this).prop('checked'));
                    updateSelectedCount();
                });

                // Individual checkbox change
                $('.product-checkbox').change(function() {
                    updateSelectedCount();
                    // If any checkbox is unchecked, uncheck the "select all" checkbox
                    if (!$(this).prop('checked')) {
                        $('#selectAll').prop('checked', false);
                    }
                    // If all checkboxes are checked, check the "select all" checkbox
                    else if ($('.product-checkbox:checked').length === $('.product-checkbox').length) {
                        $('#selectAll').prop('checked', true);
                    }
                });

                // Select all button
                $('#selectAllBtn').click(function() {
                    $('.product-checkbox').prop('checked', true);
                    $('#selectAll').prop('checked', true);
                    updateSelectedCount();
                });

                // Deselect all button
                $('#deselectAllBtn').click(function() {
                    $('.product-checkbox').prop('checked', false);
                    $('#selectAll').prop('checked', false);
                    updateSelectedCount();
                });

                // Update selected count
                function updateSelectedCount() {
                    var count = $('.product-checkbox:checked').length;
                    if (count > 0) {
                        $('#bulkDeleteBtn').text('Xóa ' + count + ' sản phẩm đã chọn');
                        $('#bulkUpdateStockBtn').text('Cập nhật tồn kho ' + count + ' sản phẩm');
                        $('#bulkExportBtn').text('Xuất ' + count + ' sản phẩm ra Excel');
                    } else {
                        $('#bulkDeleteBtn').text('Xóa đã chọn');
                        $('#bulkUpdateStockBtn').text('Cập nhật tồn kho');
                        $('#bulkExportBtn').text('Xuất Excel');
                    }
                }

                // Bulk delete
                $('#bulkDeleteBtn').click(function(e) {
                    e.preventDefault();
                    var selectedIds = [];
                    $('.product-checkbox:checked').each(function() {
                        selectedIds.push(parseInt($(this).val()));
                    });

                    if (selectedIds.length === 0) {
                        alert('Vui lòng chọn ít nhất một sản phẩm để xóa.');
                        return;
                    }

                    if (confirm('Bạn có chắc chắn muốn xóa ' + selectedIds.length + ' sản phẩm đã chọn?')) {
                        $.ajax({
                            url: '/api/BulkProducts/delete',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify(selectedIds),
                            success: function(result) {
                                if (result.success) {
                                    alert(result.message);
                                    location.reload();
                                } else {
                                    alert(result.message);
                                }
                            },
                            error: function(xhr, status, error) {
                                alert('Có lỗi xảy ra khi xóa sản phẩm: ' + error);
                            }
                        });
                    }
                });

                // Bulk update stock
                $('#bulkUpdateStockBtn').click(function(e) {
                    e.preventDefault();
                    var selectedIds = [];
                    $('.product-checkbox:checked').each(function() {
                        selectedIds.push(parseInt($(this).val()));
                    });

                    if (selectedIds.length === 0) {
                        alert('Vui lòng chọn ít nhất một sản phẩm để cập nhật tồn kho.');
                        return;
                    }

                    var newStock = prompt('Nhập số lượng tồn kho mới cho ' + selectedIds.length + ' sản phẩm đã chọn:');
                    if (newStock !== null) {
                        var quantity = parseInt(newStock);
                        if (isNaN(quantity) || quantity < 0) {
                            alert('Số lượng không hợp lệ. Vui lòng nhập số nguyên không âm.');
                            return;
                        }

                        $.ajax({
                            url: '/api/BulkProducts/updateStock',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                ids: selectedIds,
                                quantity: quantity
                            }),
                            success: function(result) {
                                if (result.success) {
                                    alert(result.message);
                                    location.reload();
                                } else {
                                    alert(result.message);
                                }
                            },
                            error: function(xhr, status, error) {
                                alert('Có lỗi xảy ra khi cập nhật tồn kho: ' + error);
                            }
                        });
                    }
                });

                // Tạo modal xuất Excel
                if ($('#exportExcelModal').length === 0) {
                    var exportModalHtml = `
                    <div class="modal fade" id="exportExcelModal" tabindex="-1" aria-labelledby="exportExcelModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="exportExcelModalLabel">Xuất sản phẩm ra Excel</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">


                                            <div class="mb-3">
                                                <label class="form-label">Lọc theo danh mục</label>
                                                <select class="form-select" id="exportCategoryFilter">
                                                    <option value="" selected>Tất cả danh mục</option>
                                                    @if (ViewBag.Categories != null)
                                                    {
                                                        foreach (var category in ViewBag.Categories)
                                                        {
                                                            <option value="@category.Id">@category.Name</option>
                                                        }
                                                    }
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Lọc theo tình trạng</label>
                                                <select class="form-select" id="exportStockFilter">
                                                    <option value="" selected>Tất cả tình trạng</option>
                                                    <option value="in-stock">Còn hàng</option>
                                                    <option value="out-of-stock">Hết hàng</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Sắp xếp theo</label>
                                                <select class="form-select" id="exportSortBy">
                                                    <option value="id-asc" selected>ID (tăng dần)</option>
                                                    <option value="id-desc">ID (giảm dần)</option>
                                                    <option value="name-asc">Tên sản phẩm (A-Z)</option>
                                                    <option value="name-desc">Tên sản phẩm (Z-A)</option>
                                                    <option value="price-asc">Giá (thấp đến cao)</option>
                                                    <option value="price-desc">Giá (cao đến thấp)</option>
                                                    <option value="stock-asc">Tồn kho (thấp đến cao)</option>
                                                    <option value="stock-desc">Tồn kho (cao đến thấp)</option>
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Lọc theo giá</label>
                                                <div class="row">
                                                    <div class="col-6">
                                                        <input type="number" class="form-control" id="exportPriceMin" placeholder="Giá tối thiểu">
                                                    </div>
                                                    <div class="col-6">
                                                        <input type="number" class="form-control" id="exportPriceMax" placeholder="Giá tối đa">
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Thêm trường ẩn để lưu trữ tùy chọn xuất mặc định -->
                                            <input type="hidden" id="exportOption" value="all">
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                                    <button type="button" class="btn btn-success" id="exportExcelSubmit">
                                        <i class="fas fa-file-export me-1"></i>Xuất Excel
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>`;

                    $('body').append(exportModalHtml);

                    // Xử lý sự kiện xuất Excel
                    $('#exportExcelSubmit').click(function() {
                        // Sử dụng giá trị mặc định 'all' cho exportOption
                        var exportOption = $('#exportOption').val();
                        var categoryId = $('#exportCategoryFilter').val();
                        var stockFilter = $('#exportStockFilter').val();
                        var sortBy = $('#exportSortBy').val();
                        var priceMin = $('#exportPriceMin').val();
                        var priceMax = $('#exportPriceMax').val();

                        // Tạo URL với tham số
                        var url = '/Excel/ExportProductsSimple?';

                        // Thêm các tùy chọn lọc và sắp xếp
                        if (categoryId) {
                            url += '&categoryId=' + categoryId;
                        }

                        if (stockFilter) {
                            url += '&stockFilter=' + stockFilter;
                        }

                        if (sortBy) {
                            url += '&sortBy=' + sortBy;
                        }

                        if (priceMin) {
                            url += '&priceMin=' + priceMin;
                        }

                        if (priceMax) {
                            url += '&priceMax=' + priceMax;
                        }

                        // Đóng modal
                        $('#exportExcelModal').modal('hide');

                        // Hiển thị thông báo đang xuất Excel
                        var toastHtml = `
                        <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
                            <div id="exportToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                                <div class="toast-header bg-success text-white">
                                    <i class="fas fa-file-excel me-2"></i>
                                    <strong class="me-auto">Xuất Excel</strong>
                                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                                </div>
                                <div class="toast-body">
                                    Đang xuất dữ liệu ra file Excel...
                                </div>
                            </div>
                        </div>`;

                        // Thêm toast vào body nếu chưa tồn tại
                        if ($('#exportToast').length === 0) {
                            $('body').append(toastHtml);
                        }

                        // Hiển thị toast
                        var exportToast = new bootstrap.Toast(document.getElementById('exportToast'));
                        exportToast.show();

                        // Sử dụng AJAX để kiểm tra trước khi mở URL
                        $.ajax({
                            url: url,
                            type: 'GET',
                            dataType: 'json',
                            success: function(response) {
                                // Nếu có lỗi, hiển thị thông báo
                                if (response && response.success === false) {
                                    // Ẩn toast thành công
                                    exportToast.hide();

                                    // Hiển thị thông báo lỗi
                                    var errorToastHtml = `
                                    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
                                        <div id="exportErrorToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                                            <div class="toast-header bg-danger text-white">
                                                <i class="fas fa-exclamation-circle me-2"></i>
                                                <strong class="me-auto">Lỗi xuất Excel</strong>
                                                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                                            </div>
                                            <div class="toast-body">
                                                ${response.message}
                                            </div>
                                        </div>
                                    </div>`;

                                    // Thêm toast vào body nếu chưa tồn tại
                                    if ($('#exportErrorToast').length === 0) {
                                        $('body').append(errorToastHtml);
                                    } else {
                                        $('#exportErrorToast .toast-body').text(response.message);
                                    }

                                    // Hiển thị toast lỗi
                                    var errorToast = new bootstrap.Toast(document.getElementById('exportErrorToast'));
                                    errorToast.show();
                                }
                            },
                            error: function(xhr, status, error) {
                                // Nếu không phải JSON response, đây là file Excel, mở trong tab mới
                                if (xhr.status === 200) {
                                    window.open(url, '_blank');
                                } else {
                                    // Hiển thị thông báo lỗi
                                    alert('Có lỗi xảy ra khi xuất Excel: ' + error);
                                }
                            }
                        });
                    });
                }

                // Bulk export button click
                $('#bulkExportBtn').click(function(e) {
                    e.preventDefault();

                    // Kiểm tra xem có sản phẩm nào được chọn không
                    var selectedIds = [];
                    $('.product-checkbox:checked').each(function() {
                        selectedIds.push($(this).val());
                    });

                    // Nếu có sản phẩm được chọn, sử dụng danh sách đó
                    if (selectedIds.length > 0) {
                        $('#exportOption').val('selected');

                        // Tạo URL trực tiếp và mở tab mới
                        var url = '/Excel/ExportProductsSimple?ids=' + selectedIds.join(',');

                        // Hiển thị thông báo đang xuất Excel
                        var toastHtml = `
                        <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
                            <div id="exportToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                                <div class="toast-header bg-success text-white">
                                    <i class="fas fa-file-excel me-2"></i>
                                    <strong class="me-auto">Xuất Excel</strong>
                                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                                </div>
                                <div class="toast-body">
                                    Đang xuất ${selectedIds.length} sản phẩm đã chọn ra file Excel...
                                </div>
                            </div>
                        </div>`;

                        // Thêm toast vào body nếu chưa tồn tại
                        if ($('#exportToast').length === 0) {
                            $('body').append(toastHtml);
                        }

                        // Hiển thị toast
                        var exportToast = new bootstrap.Toast(document.getElementById('exportToast'));
                        exportToast.show();

                        // Sử dụng AJAX để kiểm tra trước khi mở URL
                        $.ajax({
                            url: url,
                            type: 'GET',
                            dataType: 'json',
                            success: function(response) {
                                // Nếu có lỗi, hiển thị thông báo
                                if (response && response.success === false) {
                                    // Ẩn toast thành công
                                    exportToast.hide();

                                    // Hiển thị thông báo lỗi
                                    var errorToastHtml = `
                                    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
                                        <div id="exportErrorToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                                            <div class="toast-header bg-danger text-white">
                                                <i class="fas fa-exclamation-circle me-2"></i>
                                                <strong class="me-auto">Lỗi xuất Excel</strong>
                                                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                                            </div>
                                            <div class="toast-body">
                                                ${response.message}
                                            </div>
                                        </div>
                                    </div>`;

                                    // Thêm toast vào body nếu chưa tồn tại
                                    if ($('#exportErrorToast').length === 0) {
                                        $('body').append(errorToastHtml);
                                    } else {
                                        $('#exportErrorToast .toast-body').text(response.message);
                                    }

                                    // Hiển thị toast lỗi
                                    var errorToast = new bootstrap.Toast(document.getElementById('exportErrorToast'));
                                    errorToast.show();
                                }
                            },
                            error: function(xhr, status, error) {
                                // Nếu không phải JSON response, đây là file Excel, mở trong tab mới
                                if (xhr.status === 200) {
                                    window.open(url, '_blank');
                                } else {
                                    // Hiển thị thông báo lỗi
                                    alert('Có lỗi xảy ra khi xuất Excel: ' + error);
                                }
                            }
                        });
                    } else {
                        // Nếu không có sản phẩm nào được chọn, hiển thị modal để lọc
                        $('#exportOption').val('all');
                        var exportExcelModal = new bootstrap.Modal(document.getElementById('exportExcelModal'));
                        exportExcelModal.show();
                    }
                });

                // Search functionality
                $('#searchBtn').click(function() {
                    var searchText = $('#searchInput').val().toLowerCase();
                    if (searchText.trim() === '') {
                        $('tbody tr').show();
                        return;
                    }

                    $('tbody tr').each(function() {
                        var found = false;
                        var productName = $(this).find('.product-name').text().toLowerCase();
                        var productCategory = $(this).find('.badge.bg-elegant-secondary').text().toLowerCase();
                        var productPrice = $(this).find('.product-price').text().toLowerCase();

                        if (productName.includes(searchText) ||
                            productCategory.includes(searchText) ||
                            productPrice.includes(searchText)) {
                            found = true;
                        }

                        if (found) {
                            $(this).show();
                        } else {
                            $(this).hide();
                        }
                    });
                });

                // Search on enter key
                $('#searchInput').keypress(function(e) {
                    if (e.which === 13) {
                        $('#searchBtn').click();
                        return false;
                    }
                });

                // Sorting functionality
                $('.sort-option').click(function(e) {
                    e.preventDefault();
                    var sortType = $(this).data('sort');
                    var rows = $('tbody tr').get();

                    rows.sort(function(a, b) {
                        var A, B;

                        if (sortType === 'name-asc' || sortType === 'name-desc') {
                            A = $(a).find('.product-name').text().toUpperCase();
                            B = $(b).find('.product-name').text().toUpperCase();
                            return sortType === 'name-asc' ? (A < B ? -1 : A > B ? 1 : 0) : (A > B ? -1 : A < B ? 1 : 0);
                        }
                        else if (sortType === 'price-asc' || sortType === 'price-desc') {
                            A = parseInt($(a).find('.product-price').text().replace(/\D/g, ''));
                            B = parseInt($(b).find('.product-price').text().replace(/\D/g, ''));
                            return sortType === 'price-asc' ? (A - B) : (B - A);
                        }
                        else if (sortType === 'stock-asc' || sortType === 'stock-desc') {
                            var stockA = $(a).find('.product-stock').text();
                            var stockB = $(b).find('.product-stock').text();

                            if (stockA === 'Hết hàng') A = 0;
                            else A = parseInt(stockA);

                            if (stockB === 'Hết hàng') B = 0;
                            else B = parseInt(stockB);

                            return sortType === 'stock-asc' ? (A - B) : (B - A);
                        }

                        return 0;
                    });

                    $.each(rows, function(index, row) {
                        $('tbody').append(row);
                    });
                });                // Filtering functionality
                $('.filter-option').click(function(e) {
                    e.preventDefault();
                    var filterType = $(this).data('filter');
                    var categoryId = '@ViewBag.CategoryId';
                    var sortBy = '@ViewBag.SortBy';
                    var order = '@ViewBag.Order';

                    // Build URL with filtering parameters
                    var url = '@Url.Action("Index", "Product")';
                    var params = [];

                    if (categoryId && categoryId !== '') {
                        params.push('categoryId=' + categoryId);
                    }
                    if (sortBy && sortBy !== '') {
                        params.push('sortBy=' + sortBy);
                    }
                    if (order && order !== '') {
                        params.push('order=' + order);
                    }
                    if (filterType) {
                        params.push('filter=' + filterType);
                    }

                    if (params.length > 0) {
                        url += '?' + params.join('&');
                    }

                    // Navigate to filtered results
                    window.location.href = url;
                });

                // Quick edit functionality
                $('.quick-edit-btn').click(function() {
                    var id = $(this).data('id');
                    var name = $(this).data('name');
                    var price = $(this).data('price');
                    var stock = $(this).data('stock');

                    // Create modal for quick edit if it doesn't exist
                    if ($('#quickEditModal').length === 0) {
                        var modalHtml = `
                        <div class="modal fade" id="quickEditModal" tabindex="-1" aria-labelledby="quickEditModalLabel" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="quickEditModalLabel">Sửa nhanh sản phẩm</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <form id="quickEditForm">
                                            <input type="hidden" id="quickEditId">
                                            <div class="mb-3">
                                                <label for="quickEditName" class="form-label">Tên sản phẩm</label>
                                                <input type="text" class="form-control" id="quickEditName">
                                            </div>
                                            <div class="mb-3">
                                                <label for="quickEditPrice" class="form-label">Giá</label>
                                                <input type="number" class="form-control" id="quickEditPrice">
                                            </div>
                                            <div class="mb-3">
                                                <label for="quickEditStock" class="form-label">Tồn kho</label>
                                                <input type="number" class="form-control" id="quickEditStock">
                                            </div>
                                        </form>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                                        <button type="button" class="btn btn-primary" id="saveQuickEdit">Lưu thay đổi</button>
                                    </div>
                                </div>
                            </div>
                        </div>`;

                        $('body').append(modalHtml);

                        // Save quick edit
                        $('#saveQuickEdit').click(function() {
                            var editId = $('#quickEditId').val();
                            var editName = $('#quickEditName').val();
                            var editPrice = $('#quickEditPrice').val();
                            var editStock = $('#quickEditStock').val();

                            if (!editName || editName.trim() === '') {
                                alert('Tên sản phẩm không được để trống');
                                return;
                            }

                            if (isNaN(editPrice) || parseFloat(editPrice) < 0) {
                                alert('Giá không hợp lệ');
                                return;
                            }

                            if (isNaN(editStock) || parseInt(editStock) < 0) {
                                alert('Số lượng không hợp lệ');
                                return;
                            }

                            $.ajax({
                                url: '/api/BulkProducts/quickEdit',
                                type: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    id: parseInt(editId),
                                    name: editName,
                                    price: parseFloat(editPrice),
                                    quantity: parseInt(editStock)
                                }),
                                success: function(result) {
                                    if (result.success) {
                                        alert(result.message);
                                        $('#quickEditModal').modal('hide');
                                        location.reload();
                                    } else {
                                        alert(result.message);
                                    }
                                },
                                error: function(xhr, status, error) {
                                    alert('Có lỗi xảy ra khi cập nhật sản phẩm: ' + error);
                                }
                            });
                        });
                    }

                    // Set values in modal
                    $('#quickEditId').val(id);
                    $('#quickEditName').val(name);
                    $('#quickEditPrice').val(price);
                    $('#quickEditStock').val(stock);

                    // Show modal
                    var quickEditModal = new bootstrap.Modal(document.getElementById('quickEditModal'));
                    quickEditModal.show();
                });





                // Tạo modal nhập Excel
                if ($('#importExcelModal').length === 0) {
                    var importModalHtml = `
                    <div class="modal fade" id="importExcelModal" tabindex="-1" aria-labelledby="importExcelModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="importExcelModalLabel">Nhập sản phẩm từ Excel</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <form id="importExcelForm" enctype="multipart/form-data">
                                                <div class="mb-3">
                                                    <label for="excelFile" class="form-label">Chọn file Excel</label>
                                                    <input type="file" class="form-control" id="excelFile" accept=".xlsx">
                                                    <div class="form-text">Chỉ hỗ trợ file Excel (.xlsx)</div>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Tùy chọn nhập</label>
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input" type="checkbox" id="updateExisting" checked>
                                                        <label class="form-check-label" for="updateExisting">
                                                            Cập nhật sản phẩm đã tồn tại
                                                        </label>
                                                    </div>
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input" type="checkbox" id="skipErrors" checked>
                                                        <label class="form-check-label" for="skipErrors">
                                                            Bỏ qua các dòng lỗi
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="alert alert-info">
                                                    <h6><i class="fas fa-info-circle me-2"></i>Hướng dẫn</h6>
                                                    <ol class="mb-0">
                                                        <li>Tải <a href="/Excel/DownloadTemplate" target="_blank">file mẫu Excel</a></li>
                                                        <li>Điền thông tin sản phẩm vào file</li>
                                                        <li>Tải lên và nhập dữ liệu</li>
                                                    </ol>
                                                </div>
                                            </form>
                                        </div>
                                        <div class="col-md-6">
                                            <div id="importResult" class="d-none">
                                                <div class="alert alert-info">
                                                    <h6><i class="fas fa-chart-bar me-2"></i>Kết quả nhập</h6>
                                                    <div class="progress mb-3">
                                                        <div id="importProgressBar" class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                                                    </div>
                                                    <div class="row text-center">
                                                        <div class="col-4">
                                                            <div class="h4" id="totalRows">0</div>
                                                            <div class="small text-muted">Tổng số dòng</div>
                                                        </div>
                                                        <div class="col-4">
                                                            <div class="h4 text-success" id="successCount">0</div>
                                                            <div class="small text-muted">Thành công</div>
                                                        </div>
                                                        <div class="col-4">
                                                            <div class="h4 text-danger" id="errorCount">0</div>
                                                            <div class="small text-muted">Lỗi</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div id="errorList" class="d-none">
                                                    <div class="card">
                                                        <div class="card-header bg-danger text-white">
                                                            <i class="fas fa-exclamation-triangle me-2"></i>Danh sách lỗi
                                                        </div>
                                                        <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                                                            <ul id="errorListItems" class="text-danger mb-0"></ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div id="importPlaceholder" class="text-center py-5">
                                                <i class="fas fa-file-excel fa-4x text-muted mb-3"></i>
                                                <p class="text-muted">Chọn file Excel và nhấn "Nhập dữ liệu" để bắt đầu</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                                    <button type="button" class="btn btn-primary" id="importExcelSubmit">
                                        <i class="fas fa-file-import me-1"></i>Nhập dữ liệu
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>`;

                    $('body').append(importModalHtml);

                    // Xử lý sự kiện nhập Excel
                    $('#importExcelBtn').click(function(e) {
                        e.preventDefault();
                        $('#importResult').addClass('d-none');
                        $('#importPlaceholder').removeClass('d-none');
                        $('#errorList').addClass('d-none');
                        $('#excelFile').val('');
                        var importExcelModal = new bootstrap.Modal(document.getElementById('importExcelModal'));
                        importExcelModal.show();
                    });

                    $('#importExcelSubmit').click(function() {
                        var fileInput = $('#excelFile')[0];
                        if (fileInput.files.length === 0) {
                            alert('Vui lòng chọn file Excel');
                            return;
                        }

                        var formData = new FormData();
                        formData.append('file', fileInput.files[0]);
                        formData.append('updateExisting', $('#updateExisting').prop('checked'));
                        formData.append('skipErrors', $('#skipErrors').prop('checked'));

                        $('#importPlaceholder').addClass('d-none');
                        $('#importResult').removeClass('d-none');
                        $('#importProgressBar').css('width', '0%');

                        $.ajax({
                            url: '/Excel/ImportProducts',
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            beforeSend: function() {
                                $('#importExcelSubmit').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang xử lý...');
                            },
                            success: function(result) {
                                $('#importExcelSubmit').prop('disabled', false).html('<i class="fas fa-file-import me-1"></i>Nhập dữ liệu');

                                if (result.success) {
                                    // Cập nhật thông tin kết quả
                                    $('#totalRows').text(result.totalRows || 0);
                                    $('#successCount').text(result.successCount);
                                    $('#errorCount').text(result.errorCount);

                                    // Cập nhật thanh tiến trình
                                    var progressPercent = 0;
                                    if (result.totalRows > 0) {
                                        progressPercent = Math.round((result.successCount / result.totalRows) * 100);
                                    }
                                    $('#importProgressBar').css('width', progressPercent + '%');

                                    // Hiển thị danh sách lỗi nếu có
                                    if (result.errorCount > 0) {
                                        $('#errorList').removeClass('d-none');
                                        $('#errorListItems').empty();

                                        result.errors.forEach(function(error) {
                                            $('#errorListItems').append('<li>' + error + '</li>');
                                        });
                                    } else {
                                        $('#errorList').addClass('d-none');
                                    }

                                    // Hiển thị thông báo thành công
                                    if (result.successCount > 0) {
                                        // Thêm thông báo thành công
                                        var successMessage = $('<div class="alert alert-success mt-3">' +
                                            '<i class="fas fa-check-circle me-2"></i>' +
                                            'Đã nhập thành công ' + result.successCount + ' sản phẩm. Trang sẽ tải lại sau 3 giây.' +
                                            '</div>');

                                        $('#importResult').append(successMessage);

                                        // Reload sau 3 giây
                                        setTimeout(function() {
                                            location.reload();
                                        }, 3000);
                                    }
                                } else {
                                    // Hiển thị thông báo lỗi
                                    var errorMessage = $('<div class="alert alert-danger mt-3">' +
                                        '<i class="fas fa-exclamation-circle me-2"></i>' +
                                        result.message +
                                        '</div>');

                                    $('#importResult').append(errorMessage);
                                }
                            },
                            error: function(xhr, status, error) {
                                $('#importExcelSubmit').prop('disabled', false).html('<i class="fas fa-file-import me-1"></i>Nhập dữ liệu');

                                // Hiển thị thông báo lỗi
                                var errorMessage = $('<div class="alert alert-danger mt-3">' +
                                    '<i class="fas fa-exclamation-circle me-2"></i>' +
                                    'Có lỗi xảy ra khi nhập Excel: ' + error +
                                    '</div>');

                                $('#importResult').append(errorMessage);
                            }
                        });
                    });
                }
            }

            // Lưu trữ trạng thái của các nút ẩn/hiện
            var productVisibilityState = {};

            // Khởi tạo trạng thái ban đầu cho các nút
            $('.toggle-visibility-btn').each(function() {
                var $btn = $(this);
                var productId = $btn.data('id');
                var isHidden = $btn.attr('data-is-hidden') === 'true';
                productVisibilityState[productId] = isHidden;
                console.log('Khởi tạo trạng thái sản phẩm ID ' + productId + ': ' + (isHidden ? 'Đã ẩn' : 'Hiển thị'));
            });

            // Xử lý sự kiện ẩn/hiện sản phẩm
            $(document).on('click', '.toggle-visibility-btn', function() {
                var btn = $(this);
                var id = btn.data('id');
                var currentIsHidden = productVisibilityState[id];

                // Log để debug
                console.log('Nút ẩn/hiện sản phẩm ID ' + id + ' được click. Trạng thái hiện tại: ' + (currentIsHidden ? 'Đã ẩn' : 'Hiển thị'));

                // Hiển thị thông báo xác nhận
                if (confirm('Bạn có chắc chắn muốn thay đổi trạng thái hiển thị của sản phẩm này?')) {
                    // Gửi yêu cầu API để thay đổi trạng thái
                    $.ajax({
                        url: '/api/ProductVisibility/toggle',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            id: id
                        }),
                        success: function(response) {
                            if (response.success) {
                                // Cập nhật UI
                                var row = btn.closest('tr');
                                var statusCell = row.find('td:nth-child(8)'); // Cột trạng thái (cột thứ 8)
                                var isHidden = response.product.isHidden;

                                // Cập nhật UI dựa trên trạng thái mới từ server
                                if (isHidden) {
                                    // Sản phẩm đã bị ẩn
                                    statusCell.html('<span class="badge bg-warning text-dark"><i class="fas fa-eye-slash me-1"></i>Đã ẩn</span>');

                                    // Cập nhật nút thành "Hiển thị sản phẩm"
                                    btn.removeClass('btn-warning').addClass('btn-info');
                                    btn.attr('title', 'Hiển thị sản phẩm');
                                    btn.find('i').removeClass('fa-eye-slash').addClass('fa-eye');
                                } else {
                                    // Sản phẩm đang hiển thị
                                    statusCell.html('<span class="badge bg-info"><i class="fas fa-eye me-1"></i>Hiển thị</span>');

                                    // Cập nhật nút thành "Ẩn sản phẩm"
                                    btn.removeClass('btn-info').addClass('btn-warning');
                                    btn.attr('title', 'Ẩn sản phẩm');
                                    btn.find('i').removeClass('fa-eye').addClass('fa-eye-slash');
                                }

                                // Log để debug
                                console.log('Cập nhật trạng thái sản phẩm ID ' + id + ' thành ' + (isHidden ? 'Đã ẩn' : 'Hiển thị'));

                                // Cập nhật trạng thái trong biến JavaScript
                                productVisibilityState[id] = isHidden;
                                console.log('Đã cập nhật trạng thái sản phẩm ID ' + id + ' trong biến JavaScript thành ' + (isHidden ? 'Đã ẩn' : 'Hiển thị'));

                                // Cập nhật thuộc tính data-is-hidden trong DOM (chỉ để tham khảo)
                                btn.attr('data-is-hidden', isHidden.toString().toLowerCase());

                                // Hiển thị thông báo thành công
                                var toastHtml = `
                                <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
                                    <div id="visibilityToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                                        <div class="toast-header bg-success text-white">
                                            <i class="fas fa-check-circle me-2"></i>
                                            <strong class="me-auto">Thành công</strong>
                                            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                                        </div>
                                        <div class="toast-body">
                                            ${response.message}
                                        </div>
                                    </div>
                                </div>`;

                                // Thêm toast vào body nếu chưa tồn tại
                                if ($('#visibilityToast').length === 0) {
                                    $('body').append(toastHtml);
                                } else {
                                    $('#visibilityToast .toast-body').text(response.message);
                                }

                                // Hiển thị toast
                                var visibilityToast = new bootstrap.Toast(document.getElementById('visibilityToast'));
                                visibilityToast.show();
                            } else {
                                alert('Lỗi: ' + response.message);
                            }
                        },
                        error: function(xhr, status, error) {
                            alert('Có lỗi xảy ra khi cập nhật trạng thái sản phẩm: ' + error);
                        }
                    });
                }
            });

            // Hiển thị trạng thái bộ lọc hiện tại trên nút Lọc
            var currentFilter = '@ViewBag.CurrentFilter';
            var filterText = 'Lọc';
            if (currentFilter === 'in-stock') filterText = 'Còn hàng';
            else if (currentFilter === 'out-of-stock') filterText = 'Hết hàng';

            $('.btn-group:last .dropdown-toggle').html('<i class="fas fa-filter me-1"></i>' + filterText);

            // Highlight active filter option
            $('.filter-option[data-filter="' + currentFilter + '"]').addClass('active');
        });
    </script>


}
