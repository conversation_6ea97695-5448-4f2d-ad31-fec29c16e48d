/* Dashboard styles based on Bootstrap Dashboard Example */
:root {
  --sidebar-width: 250px;
  --sidebar-collapsed-width: 80px;
}

body {
  font-family: 'Poppins', sans-serif;
  background: #f8f9fa;
}

/* Disable transitions when navigating between pages */
body.disable-transitions * {
  transition: none !important;
}

.wrapper {
  display: flex;
  width: 100%;
  align-items: stretch;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Sidebar */
#sidebar {
  min-width: var(--sidebar-width);
  max-width: var(--sidebar-width);
  background: var(--elegant-dark);
  color: var(--elegant-text);
  transition: width 0.3s;
  box-shadow: 3px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

#sidebar.active {
  min-width: var(--sidebar-collapsed-width);
  max-width: var(--sidebar-collapsed-width);
  text-align: center;
}

#sidebar #sidebarCollapse {
  padding: 5px 8px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

#sidebar.active #sidebarCollapse {
  transform: rotate(180deg);
}

#sidebar.active .sidebar-header h3 {
  display: none;
}

#sidebar.active .sidebar-header strong {
  display: block;
}

#sidebar ul li a {
  text-align: left;
  padding: 10px 15px;
  display: block;
  color: var(--elegant-text);
  text-decoration: none;
  transition: background-color 0.2s, color 0.2s, border-left 0.2s;
  border-left: 3px solid transparent;
}

#sidebar.active ul li a {
  padding: 15px 10px;
  text-align: center;
  font-size: 0.85em;
  transition: background-color 0.2s, color 0.2s;
}



#sidebar.active .menu-text {
  display: none;
}

#sidebar ul li a i {
  margin-right: 10px;
  font-size: 1.2em;
  width: 20px;
  text-align: center;
}

#sidebar.active ul li a i {
  margin-right: 0;
  font-size: 1.5em;
}

#sidebar ul li a:hover {
  color: var(--elegant-gold);
  background: rgba(212, 175, 55, 0.1);
  border-left: 3px solid var(--elegant-gold);
  transition: none;
}

#sidebar ul li.active > a {
  color: var(--elegant-dark);
  background: var(--elegant-gold);
  border-left: 3px solid var(--elegant-gold-dark);
}

#sidebar .sidebar-header {
  padding: 20px;
  background: var(--elegant-black);
  border-bottom: 1px solid var(--elegant-gold);
}

#sidebar .sidebar-header h3 {
  color: var(--elegant-gold);
  margin: 0;
  font-size: 1.5rem;
}

#sidebar ul.components {
  padding: 20px 0;
  border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

#sidebar ul li {
  margin-bottom: 5px;
}

/* Content */
#content {
  width: calc(100% - var(--sidebar-width));
  padding: 20px;
  min-height: 100vh;
  transition: margin-left 0.3s, width 0.3s;
  /* margin-left: var(--sidebar-width); */
  flex: 1;
}

#content.active {
  width: calc(100% - var(--sidebar-collapsed-width));
  /* margin-left: var(--sidebar-collapsed-width); */
}

/* Navbar */
.navbar {
  padding: 15px 10px;
  background: #fff;
  border: none;
  border-radius: 0;
  margin-bottom: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Dashboard cards */
.dashboard-card {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  margin-bottom: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.125);
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.dashboard-card .card-header {
  background-color: rgba(212, 175, 55, 0.05);
  border-bottom: 1px solid rgba(212, 175, 55, 0.2);
  font-weight: 600;
  padding: 0.75rem 1.25rem;
}

.dashboard-card .card-body {
  padding: 1.25rem;
}

/* Dashboard stats */
.dashboard-stat {
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  background-color: #fff;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-left: 4px solid var(--elegant-gold);
  transition: all 0.3s ease;
}

.dashboard-stat:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.dashboard-stat-icon {
  font-size: 2rem;
  color: var(--elegant-gold);
  margin-bottom: 0.5rem;
}

.dashboard-stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: var(--elegant-dark);
}

.dashboard-stat-label {
  font-size: 0.875rem;
  color: var(--elegant-text-muted);
  margin-bottom: 0;
}

/* Dashboard table */
.dashboard-table {
  width: 100%;
  margin-bottom: 1rem;
  color: var(--elegant-dark);
}

.dashboard-table th {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid rgba(212, 175, 55, 0.2);
  background-color: rgba(212, 175, 55, 0.05);
  font-weight: 600;
}

.dashboard-table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid rgba(212, 175, 55, 0.2);
}

.dashboard-table tbody tr:hover {
  background-color: rgba(212, 175, 55, 0.05);
}

/* Media Queries */
@media (max-width: 768px) {
  #sidebar {
    min-width: var(--sidebar-collapsed-width);
    max-width: var(--sidebar-collapsed-width);
    text-align: center;
    margin-left: calc(var(--sidebar-collapsed-width) * -1);
  }

  #sidebar.active {
    margin-left: 0;
    min-width: var(--sidebar-width);
    max-width: var(--sidebar-width);
  }

  #sidebar .sidebar-header h3,
  #sidebar .sidebar-header strong {
    display: none;
  }

  #sidebar.active .sidebar-header h3 {
    display: block;
  }

  #sidebar ul li a {
    padding: 15px 10px;
    text-align: center;
    font-size: 0.85em;
  }

  #sidebar ul li a i {
    margin-right: 0;
    display: block;
    font-size: 1.5em;
    margin-bottom: 5px;
  }

  #sidebar ul li a span {
    display: none;
  }

  #sidebar.active ul li a {
    padding: 10px 15px;
    text-align: left;
    font-size: 1em;
  }

  #sidebar.active ul li a i {
    margin-right: 0.5rem;
    display: inline-block;
    font-size: 1em;
    margin-bottom: 0;
  }

  #sidebar.active ul li a span {
    display: inline-block;
  }

  #content {
    width: 100%;
    margin-left: 0;
  }

  #content.active {
    width: 100%;
    margin-left: 0;
  }
}


