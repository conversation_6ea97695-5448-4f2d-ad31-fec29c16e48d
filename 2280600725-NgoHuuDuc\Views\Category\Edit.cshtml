﻿@model NgoHuuDuc_2280600725.Models.Category
@using Microsoft.AspNetCore.Mvc.Rendering
@{
    ViewData["Title"] = "Sửa danh mục";
}

<h1>Sửa</h1>

<h4><PERSON><PERSON> mục</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" /> <!-- Ensure this hidden input is present -->
            <div class="form-group mb-3">
                <label asp-for="Name" class="control-label"></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group mb-3">
                <label asp-for="Description" class="control-label"></label>
                <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                <span asp-validation-for="Description" class="text-danger"></span>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Lưu</button>
                <a asp-action="Index" class="btn btn-secondary">Quay lại danh sách</a>
            </div>
        </form>
        @if (TempData["Success"] != null)
        {
            <div class="alert alert-success mt-3">
                @TempData["Success"]
            </div>
        }
    </div>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
