@{
    ViewData["Title"] = "Dashboard";
}

<div class="row">
    <!-- Stats -->
    <div class="col-md-3">
        <div class="dashboard-stat">
            <div class="dashboard-stat-icon">
                <i class="fas fa-box"></i>
            </div>
            <div class="dashboard-stat-value">@ViewBag.ProductCount</div>
            <div class="dashboard-stat-label">Sản phẩm</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="dashboard-stat">
            <div class="dashboard-stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="dashboard-stat-value">@ViewBag.UserCount</div>
            <div class="dashboard-stat-label">Người dùng</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="dashboard-stat">
            <div class="dashboard-stat-icon">
                <i class="fas fa-tags"></i>
            </div>
            <div class="dashboard-stat-value">@ViewBag.CategoryCount</div>
            <div class="dashboard-stat-label">Danh mục</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="dashboard-stat">
            <div class="dashboard-stat-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="dashboard-stat-value">@ViewBag.OrderCount</div>
            <div class="dashboard-stat-label">Đơn hàng</div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Products -->
    <div class="col-md-6">
        <div class="dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>Sản phẩm mới nhất</span>
                <a asp-controller="Product" asp-action="Index" class="btn btn-sm btn-elegant-secondary">Xem tất cả</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="dashboard-table">
                        <thead>
                            <tr>
                                <th>Tên sản phẩm</th>
                                <th>Danh mục</th>
                                <th>Giá</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (ViewBag.RecentProducts != null)
                            {
                                @foreach (var product in ViewBag.RecentProducts)
                                {
                                    <tr>
                                        <td>@product.Name</td>
                                        <td>@product.Category.Name</td>
                                        <td>@product.Price.ToString("N0") VNĐ</td>
                                    </tr>
                                }
                            }
                            else
                            {
                                <tr>
                                    <td colspan="3" class="text-center">Không có dữ liệu</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Users -->
    <div class="col-md-6">
        <div class="dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>Người dùng mới nhất</span>
                <a asp-controller="Account" asp-action="Index" class="btn btn-sm btn-elegant-secondary">Xem tất cả</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="dashboard-table">
                        <thead>
                            <tr>
                                <th>Tên người dùng</th>
                                <th>Email</th>
                                <th>Vai trò</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (ViewBag.RecentUsers != null)
                            {
                                @foreach (var user in ViewBag.RecentUsers)
                                {
                                    <tr>
                                        <td>@user.FullName</td>
                                        <td>@user.Email</td>
                                        <td>@user.Role</td>
                                    </tr>
                                }
                            }
                            else
                            {
                                <tr>
                                    <td colspan="3" class="text-center">Không có dữ liệu</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Quick Actions -->
    <div class="col-md-12">
        <div class="dashboard-card">
            <div class="card-header">
                <span>Thao tác nhanh</span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a asp-controller="Product" asp-action="Create" class="btn btn-elegant-primary w-100">
                            <i class="fas fa-plus-circle me-2"></i>Thêm sản phẩm mới
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a asp-controller="Category" asp-action="Create" class="btn btn-elegant-primary w-100">
                            <i class="fas fa-plus-circle me-2"></i>Thêm danh mục mới
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="#" class="btn btn-elegant-secondary w-100">
                            <i class="fas fa-file-export me-2"></i>Xuất báo cáo
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="#" class="btn btn-elegant-secondary w-100">
                            <i class="fas fa-cog me-2"></i>Cài đặt hệ thống
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
