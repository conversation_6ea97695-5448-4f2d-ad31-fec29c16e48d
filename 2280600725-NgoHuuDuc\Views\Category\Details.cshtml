﻿@model NgoHuuDuc_2280600725.Models.Category
@{
    ViewData["Title"] = "Chi tiết danh mục";
}

<h1><PERSON> tiết</h1>

<div>
    <h4><PERSON><PERSON> mục</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Name)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Name)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Description)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Description)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">Sửa</a> |
    <a asp-action="Index" class="btn btn-secondary"><PERSON>uay lại danh sách</a>
</div>